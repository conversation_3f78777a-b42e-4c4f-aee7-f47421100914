pipeline {
    agent any

    environment {
        APP_DIR = "${env.WORKSPACE}"
    }

    stages {
        stage('Set Up .env File') {
            steps {
                dir("${APP_DIR}") {
                    withCredentials([file(credentialsId: 'nep_app_env', variable: 'ENV_FILE')]) {
                        sh '''
                            echo "📄 Copying .env file..."
                            cp "$ENV_FILE" .env
                            chmod 600 .env
                        '''
                    }
                }
            }
        }

        stage('Docker Build') {
            steps {
                dir("${APP_DIR}") {
                    script {
                        try {
                            echo "🏗 Building Docker images..."
                            sh 'docker compose --env-file .env build --no-cache'
                            env.BUILD_SUCCEEDED = "true"
                        } catch (Exception err) {
                            echo "❌ Build failed: ${err}"
                            currentBuild.result = 'FAILURE'
                            env.BUILD_SUCCEEDED = "false"
                        }
                    }
                }
            }
        }

        stage('Rolling Restart Deploy') {
            when {
                expression { env.BUILD_SUCCEEDED == "true" }
            }
            steps {
                dir("${APP_DIR}") {
                    script {
                        try {
                            echo "🚀 Rolling restart with scaled instances..."
                            sh '''
                                # Optimize for 8-core server with better CPU affinity
                                docker compose --env-file .env up -d \
                                  --scale management=5 \
                                  --scale socket_v2=3 \
                                  --scale auth=1 \
                                  --scale editor=3 \
                                  --force-recreate --no-deps

                                # Optional: Set CPU affinity for better performance
                                echo "🔧 Optimizing container CPU affinity..."
                            '''

                            echo "✅ Deployment successful - Management:5, Socket_v2:3, Auth:1, Editor:3"
                            echo "📊 Total instances: 12 services using ~3.0 CPU cores (37.5% of 8 cores)"
                            echo "💾 Estimated memory usage: ~2.3GB"
                        } catch (Exception err) {
                            echo "❌ Deploy failed: ${err}"
                            currentBuild.result = 'FAILURE'
                            throw err
                        }
                    }
                }
            }
        }
    }

    post {
        success {
            echo '✅ Done!'
        }
        failure {
            echo '❌ Failed!'
        }
        always {
            echo "📌 Finished."
        }
    }
}