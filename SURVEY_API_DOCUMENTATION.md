# Survey Management API Documentation

## Overview

The Survey Management API provides comprehensive CRUD operations for managing survey questions and user submissions. It supports multiple question types, pagination, filtering, and random question sampling.

## Code Organization

The survey management system is organized in a dedicated folder structure:

```
app/v1/api/management_service/routes/survey/
├── __init__.py          # Router registration
├── models.py           # All survey-related Pydantic models
└── survey.py           # All survey API endpoints
```

## Base URL

```
/survey
```

## Authentication

- **Admin role required** for question management operations
- **User authentication required** for submissions
- Users can only view their own submissions (unless admin)

## Database Collections

### survey_questions
Stores survey questions with their options and metadata.

### survey_submissions  
Stores user responses to survey questions.

## API Endpoints

### Survey Questions

#### 1. Get All Questions (Paginated)
```http
GET /survey/questions
```

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (1-100, default: 20)
- `category` (string): Filter by category
- `status` (string): Filter by status (active, inactive, draft)
- `question_type` (string): Filter by question type
- `search` (string): Search in question text (Nepali & English)
- `sort_by` (string): Field to sort by (default: created_at)
- `sort_order` (int): Sort order (1 ascending, -1 descending)

**Authentication:** Admin required

**Response:**
```json
{
  "data": [
    {
      "id": "60f7b3b3b3b3b3b3b3b3b3b3",
      "question_text": "तपाईंको मनपर्ने रंग के हो?",
      "question_text_en": "What is your favorite color?",
      "question_type": "single_choice",
      "options": [
        {
          "key": "a",
          "text": "रातो",
          "text_en": "Red"
        }
      ],
      "is_required": true,
      "status": "active",
      "category": "preferences",
      "tags": ["color", "preference"],
      "order_index": 1,
      "created_at": "2023-07-20T10:30:00Z",
      "updated_at": "2023-07-20T10:30:00Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "total_pages": 3
  }
}
```

#### 2. Get Random Questions
```http
GET /survey/questions/random
```

**Query Parameters:**
- `count` (int): Number of random questions (1-50, required)
- `category` (string): Filter by category
- `status` (string): Filter by status (default: active)
- `question_type` (string): Filter by question type

**Authentication:** User required

**Response:**
```json
[
  {
    "id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "question_text": "तपाईंको मनपर्ने रंग के हो?",
    "question_type": "single_choice",
    "options": [...],
    "is_required": true,
    "status": "active"
  }
]
```

#### 3. Get Specific Question
```http
GET /survey/questions/{question_id}
```

**Authentication:** Admin required

#### 4. Create Question
```http
POST /survey/questions
```

**Authentication:** Admin required

**Request Body:**
```json
{
  "text": "तपाईंको मनपर्ने रंग के हो?",
  "options": ["रातो", "निलो", "हरियो", "पहेंलो"],
  "question_type": "single_choice",
  "status": "active",
  "category": "preferences"
}
```

**Response:**
```json
{
  "id": "60f7b3b3b3b3b3b3b3b3b3b3",
  "text": "तपाईंको मनपर्ने रंग के हो?",
  "question_type": "single_choice",
  "options": {
    "a": "रातो",
    "b": "निलो",
    "c": "हरियो",
    "d": "पहेंलो"
  },
  "status": "active",
  "category": "preferences",
  "created_at": "2023-07-20T10:30:00Z",
  "updated_at": "2023-07-20T10:30:00Z"
}
```

#### 4a. Create Multiple Questions (Bulk)
```http
POST /survey/questions/bulk
```

**Authentication:** Admin required

**Request Body:**
```json
{
  "questions": [
    {
      "text": "तपाईंको मनपर्ने रंग के हो?",
      "options": ["रातो", "निलो", "हरियो", "पहेंलो"],
      "question_type": "single_choice",
      "status": "active",
      "category": "preferences"
    },
    {
      "text": "तपाईं कुन खेल मन पराउनुहुन्छ?",
      "options": ["फुटबल", "क्रिकेट", "बास्केटबल"],
      "question_type": "single_choice",
      "status": "active",
      "category": "sports"
    }
  ]
}
```

#### 5. Update Question
```http
PUT /survey/questions/{question_id}
```

**Authentication:** Admin required

**Request Body:** Same as create, but all fields optional

#### 6. Delete Question
```http
DELETE /survey/questions/{question_id}
```

**Authentication:** Admin required

### Survey Submissions

#### 1. Get All Submissions (Paginated)
```http
GET /survey/submissions
```

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (1-100, default: 20)
- `user_id` (string): Filter by user ID
- `status` (string): Filter by status
- `start_date` (string): Start date filter (YYYY-MM-DD)
- `end_date` (string): End date filter (YYYY-MM-DD)
- `sort_by` (string): Field to sort by (default: submitted_at)
- `sort_order` (int): Sort order

**Authentication:** Admin required

#### 2. Get Specific Submission
```http
GET /survey/submissions/{submission_id}
```

**Authentication:** Admin required

#### 3. Get User's Submissions
```http
GET /survey/submissions/user/{user_id}
```

**Query Parameters:**
- `page` (int): Page number
- `limit` (int): Items per page
- `status` (string): Filter by status

**Authentication:** User required (own submissions only, unless admin)

#### 4. Create Submission
```http
POST /survey/submissions
```

**Authentication:** User required

**Request Body:**
```json
{
  "answers": [
    {
      "question_id": "60f7b3b3b3b3b3b3b3b3b3b3",
      "answer_value": "a",
      "answered_at": "2023-07-20T10:30:00Z"
    },
    {
      "question_id": "60f7b3b3b3b3b3b3b3b3b3b4",
      "answer_value": ["a", "b"],
      "answered_at": "2023-07-20T10:30:00Z"
    },
    {
      "question_id": "60f7b3b3b3b3b3b3b3b3b3b5",
      "answer_value": "25",
      "answer_text": "25",
      "answered_at": "2023-07-20T10:30:00Z"
    }
  ],
  "status": "completed",
  "metadata": {}
}
```

#### 5. Delete Submission
```http
DELETE /survey/submissions/{submission_id}
```

**Authentication:** Admin required

## Question Types

### single_choice
Single selection from predefined options.

### multiple_choice
Multiple selections from predefined options.

### text
Free text input.

### rating
Numeric rating scale (1-5, 1-10, etc.).

### yes_no
Simple Yes/No questions.

### scale
Scale-based questions with numeric values.

## Status Values

### Question Status
- `active`: Question is available for use
- `inactive`: Question is disabled
- `draft`: Question is in draft mode

### Submission Status
- `completed`: Submission is complete
- `partial`: Submission is partially complete
- `abandoned`: Submission was abandoned

## Error Responses

### 400 Bad Request
```json
{
  "detail": "Invalid question ID format"
}
```

### 401 Unauthorized
```json
{
  "detail": "Not authenticated"
}
```

### 403 Forbidden
```json
{
  "detail": "Access denied: Admin role required"
}
```

### 404 Not Found
```json
{
  "detail": "Survey question not found"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Failed to create survey question: Database error"
}
```

## Database Indexes

The following indexes are automatically created for optimal performance:

### survey_questions
- `status`
- `category`
- `question_type`
- `created_at`
- `order_index`
- Text index on `question_text` and `question_text_en`

### survey_submissions
- `user_id`
- `status`
- `submitted_at`
- Compound index on `user_id` and `submitted_at`

## Example Usage

### Creating a Survey Workflow

1. **Create Questions** (Admin)
2. **Get Random Questions** (User)
3. **Submit Answers** (User)
4. **View Results** (Admin)

### Sample cURL Commands

```bash
# Create a question (Admin)
curl -X POST "http://localhost:8000/survey/questions" \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "question_text": "तपाईंको मनपर्ने रंग के हो?",
    "question_type": "single_choice",
    "options": [{"key": "a", "text": "रातो"}],
    "status": "active"
  }'

# Get random questions (User)
curl -X GET "http://localhost:8000/survey/questions/random?count=5" \
  -H "Authorization: Bearer <user_token>"

# Submit answers (User)
curl -X POST "http://localhost:8000/survey/submissions" \
  -H "Authorization: Bearer <user_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "answers": [
      {
        "question_id": "60f7b3b3b3b3b3b3b3b3b3b3",
        "answer_value": "a"
      }
    ],
    "status": "completed"
  }'
```
