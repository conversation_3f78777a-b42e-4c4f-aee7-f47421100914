# User Management API

## Authentication
```bash
POST /auth/login
{
  "username": "user",
  "password": "pass", 
  "client_id": "tenant_id"
}
```

## Password Management
```bash
# Change own password
POST /auth/users/change_password
Authorization: Bearer {token}
{
  "old_password": "current",
  "new_password": "new"
}

# Reset password (admin only)
POST /auth/users/reset_password  
Authorization: Bearer {admin_token}
{
  "username": "target_user"
}
```

## User Role Management
```bash
# Change user role (admin only)
PUT /auth/users/{user_id}/role
Authorization: Bearer {admin_token}
{
  "new_role": "admin|supervisor|agent|user|editor"
}

# Get role history (admin only)
GET /auth/users/{user_id}/role-history
Authorization: Bearer {admin_token}
```

## User Preferences
```bash
# Update preferred topics
PUT /auth/preferences
Authorization: Bearer {token}
{
  "preferred_topics": ["topic_id_1", "topic_id_2"]
}
```

## User Management (Admin Only)
```bash
# Get users with pagination
GET /auth/users?page=1&limit=10&search=username&role=admin
Authorization: Bearer {admin_token}

# Get user details
GET /auth/users/{user_id}
Authorization: Bearer {admin_token}

# Delete user
DELETE /auth/users/{user_id}
Authorization: Bearer {admin_token}
```

## Role Management (Admin Only)
```bash
# Get roles
GET /auth/roles/?page=1&limit=10
Authorization: Bearer {admin_token}

# Create role
POST /auth/roles/
Authorization: Bearer {admin_token}
{
  "name": "new_role"
}

# Delete role
DELETE /auth/roles/{role_id}
Authorization: Bearer {admin_token}
```

## User Invitation (Admin Only)
```bash
# Invite user
POST /auth/users/invite
Authorization: Bearer {admin_token}
{
  "username": "newuser",
  "role": "agent"
}

# Register with token
POST /auth/users/register
{
  "token": "invitation_token",
  "password": "password",
  "full_name": "Full Name"
}
```

## Available Roles
- `admin` - Full access
- `agent` - Default role
- `supervisor` - Elevated permissions  
- `user` - Basic permissions
- `editor` - Content editing

## Quick Examples

### Make User Admin
```bash
curl -X PUT "http://localhost:8000/auth/users/{user_id}/role" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{"new_role": "admin"}'
```

### Update Topics
```bash
curl -X PUT "http://localhost:8000/auth/preferences" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{"preferred_topics": ["math", "science"]}'
```

### Reset Password
```bash
curl -X POST "http://localhost:8000/auth/users/reset_password" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{"username": "target_user"}'
```

### Delete User
```bash
curl -X DELETE "http://localhost:8000/auth/users/{user_id}" \
  -H "Authorization: Bearer {admin_token}"
```
