# User Management Documentation

## Overview
This document covers all user management features available in the Nepali App, including authentication, role management, password operations, and administrative functions.

## Table of Contents
- [Authentication](#authentication)
- [User Registration & Invitation](#user-registration--invitation)
- [Password Management](#password-management)
- [Role Management](#role-management)
- [User Administration](#user-administration)
- [Missing Features](#missing-features)

## Authentication

### Login
**Endpoint:** `POST /auth/login`

**Description:** Authenticate users with username/password or email/password.

**Request Body:**
```json
{
  "username": "string",
  "password": "string",
  "client_id": "string"
}
```

**Response:**
```json
{
  "id": "user_id",
  "access_token": "jwt_token",
  "token_type": "bearer",
  "username": "username",
  "email": "<EMAIL>",
  "role": "admin|agent|supervisor|user",
  "tenant_id": "tenant_id",
  "tenant_label": "Tenant Name",
  "tenant_slug": "tenant_slug",
  "full_name": "User Full Name",
  "auth_provider": "password|google|both"
}
```

### Google OAuth Login
**Endpoint:** `POST /auth/google`

**Description:** Authenticate users using Google OAuth.

**Request Body:**
```json
{
  "id_token": "google_id_token",
  "client_id": "tenant_client_id"
}
```

### Admin Login
**Endpoint:** `POST /auth/admin/login`

**Description:** Special login endpoint for admin users only.

**Requirements:** User must have "admin" role.

## User Registration & Invitation

### User Signup
**Endpoint:** `POST /auth/signup`

**Description:** Register new users with email/password authentication.

**Request Body:**
```json
{
  "username": "string",
  "email": "<EMAIL>",
  "password": "string",
  "full_name": "string",
  "phone_number": "string",
  "country_code": "string",
  "client_id": "tenant_client_id"
}
```

**Default Role:** New users are assigned "agent" role by default.

### Agent Invitation (Admin Only)
**Endpoint:** `POST /auth/users/invite`

**Description:** Admins can invite new agents by generating registration tokens.

**Request Body:**
```json
{
  "username": "string",
  "role": "agent|supervisor|user"
}
```

**Response:**
```json
{
  "registration_token": "token_string",
  "success": true,
  "msg": "Token Generated!"
}
```

**Token Expiry:** 7 days

### Agent Registration
**Endpoint:** `POST /auth/users/register`

**Description:** Register using invitation token.

**Request Body:**
```json
{
  "token": "registration_token",
  "password": "string",
  "full_name": "string"
}
```

## Password Management

### Change Password
**Endpoint:** `POST /auth/users/change_password`

**Description:** Users can change their own password.

**Authentication:** Required (any authenticated user)

**Request Body:**
```json
{
  "old_password": "current_password",
  "new_password": "new_password"
}
```

**Validation:**
- Current password must be correct
- New password must be different from current password

**Response:**
```json
{
  "message": "Password successfully changed."
}
```

### Reset Password (Admin Only)
**Endpoint:** `POST /auth/users/reset_password`

**Description:** Admins can reset any user's password to a random generated password.

**Authentication:** Admin role required

**Request Body:**
```json
{
  "username": "target_username"
}
```

**Response:**
```json
{
  "message": "Password reset to {random_password} successfully."
}
```

**Note:** The new password is a 10-character random hexadecimal string.

## Role Management

### Available Roles
- **admin**: Full system access, can manage users and roles
- **agent**: Default role for new users
- **supervisor**: Elevated permissions
- **user**: Basic user permissions
- **editor**: Content editing permissions

### Get All Roles
**Endpoint:** `GET /auth/roles/`

**Description:** Get paginated list of all roles.

**Authentication:** Admin role required

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)

### Create Role
**Endpoint:** `POST /auth/roles/`

**Description:** Create a new role.

**Authentication:** Admin role required

**Request Body:**
```json
{
  "name": "role_name"
}
```

### Delete Role
**Endpoint:** `DELETE /auth/roles/{role_id}`

**Description:** Delete a role.

**Authentication:** Admin role required

**Restrictions:** Cannot delete roles that are assigned to users.

## User Administration

### Get User List
**Endpoint:** `GET /auth/users` or `GET /auth/users/list`

**Description:** Get paginated list of users with filtering and search.

**Authentication:** Admin role required

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)
- `search`: Search by username or full name
- `role`: Filter by role (admin, agent, supervisor, user)

**Response:**
```json
{
  "data": [
    {
      "id": "user_id",
      "username": "username",
      "full_name": "Full Name",
      "email": "<EMAIL>",
      "role": "role_name",
      "auth_provider": "password|google",
      "created_at": "2024-01-01T00:00:00Z",
      "last_login": "2024-01-01T00:00:00Z",
      "onboarding_completed": true,
      "age": 25,
      "difficulty_level": 2,
      "preferred_topics": ["topic1", "topic2"]
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "total_pages": 10
  }
}
```

### Get User Details
**Endpoint:** `GET /auth/users/{user_id}`

**Description:** Get specific user details by ID.

**Authentication:** Admin role required

**Response:**
```json
{
  "_id": "user_id",
  "username": "username",
  "role": "role_name"
}
```

### Admin Dashboard User Details
**Endpoint:** `GET /management/admin/dashboard/user/{user_id}`

**Description:** Get comprehensive user information for admin dashboard.

**Authentication:** Admin role required

**Response:** Detailed user information including task activity and performance metrics.

## User Role Management

### Change User Role
**Endpoint:** `PUT /auth/users/{user_id}/role`

**Description:** Change a user's role (admin only).

**Authentication:** Admin role required

**Path Parameters:**
- `user_id`: User ID of the user whose role is being changed

**Request Body:**
```json
{
  "new_role": "admin|supervisor|agent|user|editor"
}
```

**Response:**
```json
{
  "message": "User role changed successfully",
  "user_id": "user_id",
  "username": "username",
  "old_role": "previous_role",
  "new_role": "new_role",
  "changed_by": "admin_username",
  "changed_at": "2024-01-01T00:00:00Z"
}
```

**Security Features:**
- Admins cannot change their own role (prevents lockout)
- Validates that the target role exists (creates if missing)
- Prevents duplicate role assignments
- Logs all role changes for audit purposes
- Tracks who made the change and when

### Get User Role History
**Endpoint:** `GET /auth/users/{user_id}/role-history`

**Description:** Get role change information for a specific user (admin only).

**Authentication:** Admin role required

**Response:**
```json
{
  "user_id": "user_id",
  "username": "username",
  "current_role": "current_role",
  "role_changed_at": "2024-01-01T00:00:00Z",
  "role_changed_by": "admin_username",
  "account_created_at": "2024-01-01T00:00:00Z"
}
```

## Missing Features

### Additional Missing Features
- **User Profile Update:** No endpoint to update user profile information (full_name, email, etc.)
- **User Deactivation:** No soft delete or deactivation functionality
- **Bulk User Operations:** No bulk role changes or user management
- **Password Policy Enforcement:** No password complexity requirements
- **Account Lockout:** No failed login attempt tracking or account lockout

## Security Considerations

### Authentication
- JWT tokens expire after 2 hours
- Passwords are hashed using secure hashing algorithms
- Google OAuth integration for enhanced security

### Authorization
- Role-based access control (RBAC) implemented
- Admin-only endpoints properly protected
- Tenant isolation maintained

### Password Security
- Password verification before changes
- Random password generation for resets
- Secure password hashing

## Error Handling

### Common Error Responses
- **400 Bad Request:** Invalid input data or business logic violations
- **401 Unauthorized:** Missing or invalid authentication
- **403 Forbidden:** Insufficient permissions
- **404 Not Found:** User or resource not found
- **500 Internal Server Error:** Server-side errors

### Example Error Response
```json
{
  "detail": "Error message describing the issue"
}
```

## Best Practices

1. **Always use admin authentication** for user management operations
2. **Validate user input** before processing requests
3. **Log important operations** for audit trails
4. **Handle errors gracefully** with appropriate HTTP status codes
5. **Use pagination** for list endpoints to manage large datasets
6. **Implement proper tenant isolation** for multi-tenant environments

## API Testing

### Using cURL Examples

**Login:**
```bash
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password", "client_id": "tenant_id"}'
```

**Get Users (with admin token):**
```bash
curl -X GET "http://localhost:8000/auth/users?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Change Password:**
```bash
curl -X POST "http://localhost:8000/auth/users/change_password" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"old_password": "old", "new_password": "new"}'
```
