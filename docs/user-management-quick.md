# User Management Quick Reference

## 🔐 Authentication

### Login
```bash
POST /auth/login
{
  "username": "user",
  "password": "pass",
  "client_id": "tenant_id"
}
```

### Google Login
```bash
POST /auth/google
{
  "id_token": "google_token",
  "client_id": "tenant_id"
}
```

## 👥 User Management

### Get Users (Admin Only)
```bash
GET /auth/users?page=1&limit=10&search=username&role=admin
Authorization: Bearer {admin_token}
```

### Get User Details (Admin Only)
```bash
GET /auth/users/{user_id}
Authorization: Bearer {admin_token}
```

### Invite User (Admin Only)
```bash
POST /auth/users/invite
Authorization: Bearer {admin_token}
{
  "username": "newuser",
  "role": "agent"
}
```

### Register with Token
```bash
POST /auth/users/register
{
  "token": "invitation_token",
  "password": "password",
  "full_name": "Full Name"
}
```

## 🔑 Password Management

### Change Password (Self)
```bash
POST /auth/users/change_password
Authorization: Bearer {user_token}
{
  "old_password": "current",
  "new_password": "new"
}
```

### Reset Password (Admin Only)
```bash
POST /auth/users/reset_password
Authorization: Bearer {admin_token}
{
  "username": "target_user"
}
```

## 🎭 Role Management

### Get Roles (Admin Only)
```bash
GET /auth/roles/?page=1&limit=10
Authorization: Bearer {admin_token}
```

### Create Role (Admin Only)
```bash
POST /auth/roles/
Authorization: Bearer {admin_token}
{
  "name": "new_role"
}
```

### Delete Role (Admin Only)
```bash
DELETE /auth/roles/{role_id}
Authorization: Bearer {admin_token}
```

### Change User Role (Admin Only)
```bash
PUT /auth/users/{user_id}/role
Authorization: Bearer {admin_token}
{
  "new_role": "admin"
}
```

### Get User Role History (Admin Only)
```bash
GET /auth/users/{user_id}/role-history
Authorization: Bearer {admin_token}
```

## ⚠️ Missing Features

### ❌ Update User Profile
**NOT AVAILABLE** - No endpoint to update user information (email, name, etc.)

**Needed:** `PATCH /auth/users/{user_id}`

## 📋 Available Roles
- `admin` - Full system access
- `agent` - Default role for new users  
- `supervisor` - Elevated permissions
- `user` - Basic permissions
- `editor` - Content editing permissions

## 🚨 Important Notes

1. **Admin Required:** Most user management operations require admin role
2. **Token Expiry:** JWT tokens expire after 2 hours
3. **Invitation Expiry:** Invitation tokens expire after 7 days
4. **Default Role:** New signups get "agent" role automatically
5. **Role Deletion:** Cannot delete roles that are assigned to users

## 🔧 Quick Setup

1. **Create Admin User:** Use signup endpoint, then manually update role in database
2. **Invite Users:** Use admin account to invite other users
3. **Manage Roles:** Create custom roles as needed
4. **Reset Passwords:** Use admin reset for forgotten passwords

## 📝 Response Format

**Success Response:**
```json
{
  "access_token": "jwt_token",
  "username": "user",
  "role": "admin",
  "tenant_id": "tenant"
}
```

**Error Response:**
```json
{
  "detail": "Error message"
}
```

## 🛠️ Common Tasks

### Make User Admin
```bash
curl -X PUT "http://localhost:8000/auth/users/{user_id}/role" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{"new_role": "admin"}'
```

### List All Users
```bash
curl -X GET "http://localhost:8000/auth/users" \
  -H "Authorization: Bearer {admin_token}"
```

### Reset User Password
```bash
curl -X POST "http://localhost:8000/auth/users/reset_password" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{"username": "target_user"}'
```

### Create New Role
```bash
curl -X POST "http://localhost:8000/auth/roles/" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{"name": "custom_role"}'
```

DELETE /auth/users/{user_id}
curl -X DELETE "http://localhost:8000/auth/users/{user_id}" \
  -H "Authorization: Bearer {admin_token}"


  response
  {
  "message": "User deleted successfully",
  "user_id": "user_id",
  "username": "deleted_username", 
  "deleted_by": "admin_username",
  "deleted_at": "2024-01-01T00:00:00Z"
}