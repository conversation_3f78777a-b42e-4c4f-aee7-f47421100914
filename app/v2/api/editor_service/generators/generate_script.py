# To run this code you need to install the following dependencies:
# pip install google-genai

import base64
import os
import re
from google import genai
from google.genai import types
import json


def clean_json_response(response_text: str) -> str:
    """
    Clean AI response text to make it valid JSON by removing control characters
    and fixing common formatting issues.

    Args:
        response_text: Raw response text from AI model

    Returns:
        Cleaned text ready for JSON parsing
    """
    # Remove any markdown code block formatting
    cleaned = response_text.strip()
    if cleaned.startswith('```json'):
        cleaned = cleaned[7:]
    if cleaned.startswith('```'):
        cleaned = cleaned[3:]
    if cleaned.endswith('```'):
        cleaned = cleaned[:-3]

    # Remove control characters except for allowed ones (newlines in strings are OK)
    # This regex removes control characters but preserves newlines within quoted strings
    cleaned = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', cleaned)

    # Fix common JSON issues
    cleaned = cleaned.strip()

    return cleaned


def safe_json_parse(response_text: str) -> dict:
    """
    Safely parse JSON response with error handling and fallback.

    Args:
        response_text: Raw response text from AI model

    Returns:
        Parsed JSON as dictionary, or error structure if parsing fails
    """
    try:
        # First, clean the response
        cleaned_text = clean_json_response(response_text)
        print(f"Cleaned text for JSON parsing: {cleaned_text[:200]}...")

        # Try to parse the cleaned JSON
        parsed_json = json.loads(cleaned_text)
        print(f"Successfully parsed JSON: {type(parsed_json)}")
        return parsed_json

    except json.JSONDecodeError as e:
        print(f"JSON parsing error in generate_script: {e}")
        print(f"Problematic text (first 500 chars): {response_text[:500]}")

        # Try to extract story content even if JSON is malformed
        # Look for story content between quotes or after "story"
        story_content = _extract_story_fallback(response_text)

        if story_content:
            print(f"Extracted story content as fallback: {story_content[:100]}...")
            return {"story": story_content}

        # Return a fallback structure
        return {
            "story": "Error generating story due to JSON parsing issue.",
            "error": f"JSON parsing failed: {str(e)}",
            "raw_response": response_text[:1000]  # Include first 1000 chars for debugging
        }
    except Exception as e:
        print(f"Unexpected error in JSON parsing: {e}")
        return {
            "story": "Error generating story due to unexpected issue.",
            "error": f"Unexpected error: {str(e)}"
        }


def _extract_story_fallback(response_text: str) -> str:
    """
    Try to extract story content from malformed response as fallback.

    Args:
        response_text: Raw response text from AI model

    Returns:
        Extracted story content or None if not found
    """
    try:
        # Remove any markdown formatting
        text = response_text.strip()
        if text.startswith('```'):
            text = re.sub(r'^```[a-z]*\n?', '', text, flags=re.MULTILINE)
            text = re.sub(r'\n?```$', '', text)

        # Try to find story content after "story": pattern
        story_match = re.search(r'"story"\s*:\s*"([^"]+)"', text, re.DOTALL)
        if story_match:
            return story_match.group(1)

        # Try to find story content in single quotes
        story_match = re.search(r"'story'\s*:\s*'([^']+)'", text, re.DOTALL)
        if story_match:
            return story_match.group(1)

        # Try to find any Nepali text content (contains Devanagari characters)
        nepali_match = re.search(r'[\u0900-\u097F][^"\']*[\u0900-\u097F]', text)
        if nepali_match:
            return nepali_match.group(0).strip()

        # If response is mostly text without JSON structure, use it as story
        if len(text) > 50 and not text.startswith('{') and '"' not in text[:20]:
            return text.strip()

        return None

    except Exception as e:
        print(f"Error in fallback extraction: {e}")
        return None

async  def generate(content):
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.0-flash"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text=content),
            ],
        )
    ]
    generate_content_config = types.GenerateContentConfig(
        response_mime_type="application/json",
        system_instruction=[
            types.Part.from_text(text="""
                                 You are a native Nepali storyteller with deep knowledge of Nepal’s geography, history, culture, and everyday life.

Your job is to generate a short, fictional, emotional, and realistic story **in the Nepali language** that can help learners understand Nepal’s heritage, values, and lifestyle.

Make it sound like you’re telling the story aloud to a friend or a child in a warm, heartfelt tone. Use **spoken Nepali**, not overly formal or literary language.

Do not explain the story. Do not translate. Just return the final story in Nepali that sounds natural when read aloud.

Guidelines:
                                 
- Be written in **pure Nepali language**, using simple and clear vocabulary suitable for children.  
- Be told in  Character Driven **first-person narration**, like the storyteller is sharing their own memory or experience.  
- Start with a traditional Nepali storytelling phrase like "एकादेशको कुरा हो..." or "म सानो हुँदा...".
                                 
The story should be based on the following user-provided information.

IMPORTANT: You MUST return ONLY valid JSON in this exact format:
{"story": "your nepali story here"}

Do not include any other text, explanations, or formatting. Only return the JSON object with the story content in Nepali.
"""),
        ],
    )

    response =  client.models.generate_content(
        model=model,
        contents=contents,
        config=generate_content_config,
    )


    print(f"Raw response text (first 200 chars): {response.text[:200]}")
    print(f"Full response length: {len(response.text)}")

    output = safe_json_parse(response.text)
    print(f"Parsed output type: {type(output)}")
    print(f"Parsed output keys: {output.keys() if isinstance(output, dict) else 'Not a dict'}")

    story_script = output.get("story", None)
    if not story_script:
        print(f"No story found in output: {output}")
        raise Exception("Story generation failed - no story content found")

    print(f"Successfully extracted story (length: {len(story_script)})")
    return story_script, response.usage_metadata
if __name__ == "__main__":
    import asyncio  
    content="""A 10-year-old boy named Arjun who lives near Phewa Lake in Pokhara. His grandfather tells him stories about Gurkha soldiers. He wants to be brave like them. He speaks Nepali and loves boating."""
    asyncio.run(generate(content))
