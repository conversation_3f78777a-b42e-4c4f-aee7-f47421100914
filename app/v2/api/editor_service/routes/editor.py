from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from app.shared.models.user import UserTenantDB
from app.shared.security import get_tenant_info
from app.shared.utils.logger import setup_new_logging
from app.v2.api.editor_service.creator import save_to_database
loggers = setup_new_logging(__name__)


router = APIRouter( 
    tags=["Curated Editor"],
    responses={404: {"description": "Not found"}}
)

from app.shared.models.curated_content import CuratedContentRequest

@router.post("/generate")
async def generate_curated_content(
    request: CuratedContentRequest,
    background_tasks: BackgroundTasks,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Generate curated content based on the provided input.

    This endpoint generates curated content including tasks and stories,
    saves them to the database, and starts background media generation.

    Args:
        request: CuratedContentRequest containing the story prompt
        current_user: Current authenticated user

    Returns:
        CuratedContentResponse with generation results and metadata
    """
    try:
        loggers.info(f"Starting curated content generation for user {current_user.user.id}")

        # Call the save_to_database function directly to get the result
        result = await save_to_database(request, current_user,background_tasks)

        # Return the result which already matches CuratedContentResponse format
        return result
        # print(f"Request content: {request.quiz_instructions}")
        # return {"message": "Curated content generation started in background"}

    except Exception as e:
        loggers.error(f"Error generating curated content: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate curated content")




