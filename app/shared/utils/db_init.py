"""
Database initialization utilities for MongoDB.
"""
import logging
from pymongo.database import Database
from pymongo import AsyncMongoClient
from app.shared.db_enums import CollectionName

logger = logging.getLogger(__name__)

async def ensure_indexes(db: Database) -> None:
    """
    Ensure that all required indexes exist in the database.
    
    Args:
        db: The MongoDB database
    """
    try:
        # Task sets indexes
        await db[CollectionName.TASK_SETS].create_index("user_id")
        await db[CollectionName.TASK_SETS].create_index("created_at")
        await db[CollectionName.TASK_SETS].create_index("status")
        await db[CollectionName.TASK_SETS].create_index([("user_id", 1), ("created_at", -1)])
        
        # Task items indexes
        await db[CollectionName.TASK_ITEMS].create_index("type")
        await db[CollectionName.TASK_ITEMS].create_index("status")
        
        # Task history indexes
        await db[CollectionName.TASK_HISTORY].create_index("user_id")
        await db[CollectionName.TASK_HISTORY].create_index("task_set_id")
        await db[CollectionName.TASK_HISTORY].create_index("submitted_at")

        # Survey questions indexes
        await db[CollectionName.SURVEY_QUESTIONS].create_index("status")
        await db[CollectionName.SURVEY_QUESTIONS].create_index("category")
        await db[CollectionName.SURVEY_QUESTIONS].create_index("question_type")
        await db[CollectionName.SURVEY_QUESTIONS].create_index("created_at")
        await db[CollectionName.SURVEY_QUESTIONS].create_index("order_index")
        await db[CollectionName.SURVEY_QUESTIONS].create_index([("question_text", "text"), ("question_text_en", "text")])

        # Survey submissions indexes
        await db[CollectionName.SURVEY_SUBMISSIONS].create_index("user_id")
        await db[CollectionName.SURVEY_SUBMISSIONS].create_index("status")
        await db[CollectionName.SURVEY_SUBMISSIONS].create_index("submitted_at")
        await db[CollectionName.SURVEY_SUBMISSIONS].create_index([("user_id", 1), ("submitted_at", -1)])
        
        # Story steps indexes
        await db[CollectionName.STORY_STEPS].create_index("user_id")
        await db[CollectionName.STORY_STEPS].create_index("collection_id")
        await db[CollectionName.STORY_STEPS].create_index("created_at")
        await db[CollectionName.STORY_STEPS].create_index([("user_id", 1), ("created_at", -1)])


        logger.info("MongoDB indexes created successfully")
    except Exception as e:
        logger.error(f"Error creating MongoDB indexes: {str(e)}")

async def ensure_collections(db: Database) -> None:
    """
    Ensure that all required collections exist in the database.
    
    Args:
        db: The MongoDB database
    """
    try:
        # Get existing collections
        existing_collections = await db.list_collection_names()
        
        # Create collections if they don't exist
        for collection_name in CollectionName:
            if collection_name not in existing_collections:
                await db.create_collection(collection_name)
                logger.info(f"Created collection: {collection_name}")
        
        # Create indexes
        await ensure_indexes(db)
        
        logger.info("MongoDB collections initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing MongoDB collections: {str(e)}")
