"""
Models for curated content generation and management.

This module contains Pydantic models used for curated content operations,
including request/response models and data structures.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from bson import ObjectId


class CuratedContentRequest(BaseModel):
    """
    Request model for generating curated content.
    
    Attributes:
        content (str): The story prompt or content to be generated from.
    """
    content: str = Field(..., description="Story prompt or content to generate curated content from")
    quiz_instructions: Optional[str] = Field(None, description="Instructions for quiz generation")
    theme_id: Optional[str] = Field(None, description="Theme ID to generate content for")


# class CuratedContentResponse(BaseModel):
#     """
#     Response model for curated content generation.
    
#     Attributes:
#         status (str): Status of the generation process
#         message (str): Human-readable message about the result
#         task_set_id (str): ID of the generated task set
#         task_count (int): Number of task items generated
#         story_count (int): Number of story items generated
#         text_tasks_ready (int): Number of text tasks ready immediately
#         media_tasks_pending (int): Number of media tasks being processed in background
#         metadata (Dict[str, Any]): Additional metadata about the generation
#     """
#     status: str
#     message: str
#     task_set_id: str
#     task_count: int
#     story_count: int
#     text_tasks_ready: int
#     media_tasks_pending: int
#     metadata: Dict[str, Any]


class TaskItemData(BaseModel):
    """
    Data structure for a task item following v2 pattern.
    """
    type: str
    title: str
    question: Dict[str, Any]
    correct_answer: Dict[str, Any]
    user_answer: Optional[Dict[str, Any]] = None
    status: str = "pending"
    result: Optional[str] = None
    remark: Optional[str] = None
    total_score: int = 10
    scored: int = 0
    submitted: bool = False
    submitted_at: Optional[datetime] = None
    attempts_count: int = 0
    difficulty_level: int = 1
    metadata: Dict[str, Any] = {}


class StoryItemData(BaseModel):
    """
    Data structure for a story item following v2 pattern.
    """
    stage: int
    script: str
    image: str  # Image description
    thumbnail: str = "📖"
    audio_metadata: Dict[str, Any] = {}
    image_metadata: Dict[str, Any] = {}


class TaskSetData(BaseModel):
    """
    Data structure for a task set following v2 pattern.
    """
    title: str
    input_type: str = "text"
    tasks: List[str] = []  # Task IDs
    theme_id: Optional[Any] = None
    stories: List[str] = []  # Story IDs
    total_tasks: int
    total_stories: int
    text_tasks_ready: int
    media_tasks_pending: int
    attempted_tasks: int = 0
    total_verified: int = 0
    status: str = "pending"
    gentype: str = "primary"
    has_follow_up: bool = False
    total_score: int = 0
    scored: int = 0
    attempts_count: int = 0
    metadata: Dict[str, Any] = {}
