"""
Survey management router for CRUD operations on survey questions and submissions.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from typing import Dict, Any, List, Optional
from bson import ObjectId
from datetime import datetime, timezone

from app.shared.security import get_tenant_info, require_roles
from app.shared.models.user import UserTenantDB
from app.v1.api.management_service.routes.survey.models import (
    SurveyQuestionCreate, SurveyQuestionUpdate,
    SurveyQuestionResponse, SurveySubmissionCreate, SurveySubmissionResponse,
    SurveyQuestionStatus, SurveySubmissionStatus,
    BulkSurveyQuestionCreate
)
from app.shared.models.pagination import PaginationResponse, PaginationMeta
from app.shared.utils.logger import setup_new_logging
from app.shared.utils.mongodb import convert_object_ids
from app.shared.db_enums import CollectionName

# Configure logging
logger = setup_new_logging(__name__)

router = APIRouter(
    responses={404: {"description": "Not found"}}
)


# Survey Questions CRUD Operations

@router.get("/questions", response_model=PaginationResponse[SurveyQuestionResponse])
async def get_survey_questions(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    category: Optional[str] = Query(None, description="Filter by category"),
    status: Optional[SurveyQuestionStatus] = Query(None, description="Filter by status"),
    question_type: Optional[str] = Query(None, description="Filter by question type"),
    search: Optional[str] = Query(None, description="Search in question text"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_order: int = Query(-1, ge=-1, le=1, description="Sort order (1 for ascending, -1 for descending)"),
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> PaginationResponse[SurveyQuestionResponse]:
    """
    Get survey questions with pagination and filtering.
    
    Args:
        page: Page number
        limit: Number of items per page
        category: Filter by category
        status: Filter by status
        question_type: Filter by question type
        search: Search in question text
        sort_by: Field to sort by
        sort_order: Sort order
        user_tenant: User tenant information
        
    Returns:
        Paginated list of survey questions
    """
    try:
        collection = user_tenant.async_db[CollectionName.SURVEY_QUESTIONS]
        
        # Build filter query
        filter_query = {}
        
        if category:
            filter_query["category"] = category
            
        if status:
            filter_query["status"] = status
            
        if question_type:
            filter_query["question_type"] = question_type
            
        if search:
            filter_query["$or"] = [
                {"question_text": {"$regex": search, "$options": "i"}},
                {"question_text_en": {"$regex": search, "$options": "i"}}
            ]
        
        # Calculate skip value
        skip = (page - 1) * limit
        
        # Get total count
        total = await collection.count_documents(filter_query)
        
        # Get paginated data with sorting
        cursor = collection.find(filter_query).sort(sort_by, sort_order).skip(skip).limit(limit)
        questions = await cursor.to_list(length=None)
        
        # Convert ObjectIds to strings
        questions = convert_object_ids(questions)
        
        # Create response models
        question_responses = [
            SurveyQuestionResponse(**question) for question in questions
        ]
        
        # Calculate pagination metadata
        total_pages = (total + limit - 1) // limit
        
        pagination_meta = PaginationMeta(
            page=page,
            limit=limit,
            total=total,
            total_pages=total_pages
        )
        
        return PaginationResponse[SurveyQuestionResponse](
            data=question_responses,
            meta=pagination_meta
        )
        
    except Exception as e:
        logger.error(f"Error getting survey questions: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve survey questions: {str(e)}")


@router.get("/questions/random", response_model=List[SurveyQuestionResponse])
async def get_random_survey_questions(
    count: int = Query(..., ge=1, le=50, description="Number of random questions"),
    category: Optional[str] = Query(None, description="Filter by category"),
    status: SurveyQuestionStatus = Query(SurveyQuestionStatus.ACTIVE, description="Filter by status"),
    question_type: Optional[str] = Query(None, description="Filter by question type"),
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> List[SurveyQuestionResponse]:
    """
    Get random survey questions.
    
    Args:
        count: Number of random questions to retrieve
        category: Filter by category
        status: Filter by status
        question_type: Filter by question type
        user_tenant: User tenant information
        
    Returns:
        List of random survey questions
    """
    try:
        collection = user_tenant.async_db[CollectionName.SURVEY_QUESTIONS]
        
        # Build filter query
        filter_query = {"status": status}
        
        if category:
            filter_query["category"] = category
            
        if question_type:
            filter_query["question_type"] = question_type
        
        # Use MongoDB aggregation pipeline for random sampling
        pipeline = [
            {"$match": filter_query},
            {"$sample": {"size": count}}
        ]

        questions_cur = await collection.aggregate(pipeline)
        questions = await questions_cur.to_list(length=None)
        
        # Convert ObjectIds to strings
        questions = convert_object_ids(questions)
        
        # Create response models
        question_responses = [
            SurveyQuestionResponse(**question) for question in questions
        ]
        
        return question_responses
        
    except Exception as e:
        logger.error(f"Error getting random survey questions: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve random survey questions: {str(e)}")


@router.get("/questions/{question_id}", response_model=SurveyQuestionResponse)
async def get_survey_question(
    question_id: str,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> SurveyQuestionResponse:
    """
    Get a specific survey question by ID.
    
    Args:
        question_id: Survey question ID
        user_tenant: User tenant information
        
    Returns:
        Survey question details
    """
    try:
        collection = user_tenant.async_db[CollectionName.SURVEY_QUESTIONS]
        
        # Validate ObjectId
        if not ObjectId.is_valid(question_id):
            raise HTTPException(status_code=400, detail="Invalid question ID format")
        
        question = await collection.find_one({"_id": ObjectId(question_id)})
        
        if not question:
            raise HTTPException(status_code=404, detail="Survey question not found")
        
        # Convert ObjectIds to strings
        question = convert_object_ids(question)
        
        return SurveyQuestionResponse(**question)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting survey question {question_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve survey question: {str(e)}")


@router.post("/questions", response_model=SurveyQuestionResponse)
async def create_survey_question(
    question_data: SurveyQuestionCreate,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> SurveyQuestionResponse:
    """
    Create a new survey question (simplified format).

    Args:
        question_data: Survey question data (simplified format)
        user_tenant: User tenant information

    Returns:
        Created survey question
    """
    try:
        collection = user_tenant.async_db[CollectionName.SURVEY_QUESTIONS]

        # Convert list of options to dict format (a: option1, b: option2, etc.)
        options_dict = {}
        for i, option_text in enumerate(question_data.options):
            option_key = chr(ord('a') + i)  # Generate keys: a, b, c, d, etc.
            options_dict[option_key] = option_text

        # Create survey question document
        question_doc = {
            "text": question_data.text,
            "question_type": question_data.question_type,
            "options": options_dict,
            "status": question_data.status,
            "category": question_data.category,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "created_by": ObjectId(user_tenant.user.id),
            "updated_by": ObjectId(user_tenant.user.id)
        }

        # Insert the question
        result = await collection.insert_one(question_doc)

        # Retrieve the created question
        created_question = await collection.find_one({"_id": result.inserted_id})

        # Convert ObjectIds to strings
        created_question = convert_object_ids(created_question)

        logger.info(f"Created survey question {result.inserted_id} by user {user_tenant.user.id}")

        return SurveyQuestionResponse(**created_question)

    except Exception as e:
        logger.error(f"Error creating survey question: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create survey question: {str(e)}")


@router.post("/questions/bulk", response_model=List[SurveyQuestionResponse])
async def create_bulk_survey_questions(
    questions_data: BulkSurveyQuestionCreate,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> List[SurveyQuestionResponse]:
    """
    Create multiple survey questions at once.

    Args:
        questions_data: Bulk survey questions data
        user_tenant: User tenant information

    Returns:
        List of created survey questions
    """
    try:
        collection = user_tenant.async_db[CollectionName.SURVEY_QUESTIONS]
        created_questions = []

        for question_data in questions_data.questions:
            # Convert list of options to dict format (a: option1, b: option2, etc.)
            options_dict = {}
            for i, option_text in enumerate(question_data.options):
                option_key = chr(ord('a') + i)  # Generate keys: a, b, c, d, etc.
                options_dict[option_key] = option_text

            # Create survey question document
            question_doc = {
                "text": question_data.text,
                "question_type": question_data.question_type,
                "options": options_dict,
                "status": question_data.status,
                "category": question_data.category,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "created_by": ObjectId(user_tenant.user.id),
                "updated_by": ObjectId(user_tenant.user.id)
            }

            # Insert the question
            result = await collection.insert_one(question_doc)

            # Retrieve the created question
            created_question = await collection.find_one({"_id": result.inserted_id})

            # Convert ObjectIds to strings
            created_question = convert_object_ids(created_question)

            created_questions.append(SurveyQuestionResponse(**created_question))

            logger.info(f"Created bulk survey question {result.inserted_id} by user {user_tenant.user.id}")

        logger.info(f"Created {len(created_questions)} survey questions in bulk by user {user_tenant.user.id}")

        return created_questions

    except Exception as e:
        logger.error(f"Error creating bulk survey questions: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create bulk survey questions: {str(e)}")





@router.put("/questions/{question_id}", response_model=SurveyQuestionResponse)
async def update_survey_question(
    question_id: str,
    question_data: SurveyQuestionUpdate,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> SurveyQuestionResponse:
    """
    Update a survey question.
    
    Args:
        question_id: Survey question ID
        question_data: Updated question data
        user_tenant: User tenant information
        
    Returns:
        Updated survey question
    """
    try:
        collection = user_tenant.async_db[CollectionName.SURVEY_QUESTIONS]
        
        # Validate ObjectId
        if not ObjectId.is_valid(question_id):
            raise HTTPException(status_code=400, detail="Invalid question ID format")
        
        # Build update document (only include non-None fields)
        update_data = {}

        if question_data.text is not None:
            update_data["text"] = question_data.text

        if question_data.options is not None:
            # Convert list of options to dict format
            options_dict = {}
            for i, option_text in enumerate(question_data.options):
                option_key = chr(ord('a') + i)
                options_dict[option_key] = option_text
            update_data["options"] = options_dict

        if question_data.question_type is not None:
            update_data["question_type"] = question_data.question_type

        if question_data.status is not None:
            update_data["status"] = question_data.status

        if question_data.category is not None:
            update_data["category"] = question_data.category

        if not update_data:
            raise HTTPException(status_code=400, detail="No fields provided for update")

        update_data["updated_at"] = datetime.now(timezone.utc)
        update_data["updated_by"] = ObjectId(user_tenant.user.id)
        
        # Update the question
        result = await collection.update_one(
            {"_id": ObjectId(question_id)},
            {"$set": update_data}
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Survey question not found")
        
        # Retrieve the updated question
        updated_question = await collection.find_one({"_id": ObjectId(question_id)})
        
        # Convert ObjectIds to strings
        updated_question = convert_object_ids(updated_question)
        
        logger.info(f"Updated survey question {question_id} by user {user_tenant.user.id}")
        
        return SurveyQuestionResponse(**updated_question)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating survey question {question_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update survey question: {str(e)}")


@router.delete("/questions/{question_id}")
async def delete_survey_question(
    question_id: str,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Delete a survey question.
    
    Args:
        question_id: Survey question ID
        user_tenant: User tenant information
        
    Returns:
        Success message
    """
    try:
        collection = user_tenant.async_db[CollectionName.SURVEY_QUESTIONS]
        
        # Validate ObjectId
        if not ObjectId.is_valid(question_id):
            raise HTTPException(status_code=400, detail="Invalid question ID format")
        
        # Delete the question
        result = await collection.delete_one({"_id": ObjectId(question_id)})
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Survey question not found")
        
        logger.info(f"Deleted survey question {question_id} by user {user_tenant.user.id}")

        return {
            "success": True,
            "message": "Survey question deleted successfully",
            "question_id": question_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting survey question {question_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete survey question: {str(e)}")


# Survey Submissions CRUD Operations

@router.get("/submissions", response_model=PaginationResponse[SurveySubmissionResponse])
async def get_survey_submissions(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    status: Optional[SurveySubmissionStatus] = Query(None, description="Filter by status"),
    start_date: Optional[str] = Query(None, description="Start date filter (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD)"),
    sort_by: str = Query("submitted_at", description="Field to sort by"),
    sort_order: int = Query(-1, ge=-1, le=1, description="Sort order (1 for ascending, -1 for descending)"),
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> PaginationResponse[SurveySubmissionResponse]:
    """
    Get survey submissions with pagination and filtering.

    Args:
        page: Page number
        limit: Number of items per page
        user_id: Filter by user ID
        status: Filter by status
        start_date: Start date filter
        end_date: End date filter
        sort_by: Field to sort by
        sort_order: Sort order
        user_tenant: User tenant information

    Returns:
        Paginated list of survey submissions
    """
    try:
        collection = user_tenant.async_db[CollectionName.SURVEY_SUBMISSIONS]

        # Build filter query
        filter_query = {}

        if user_id:
            # Convert user_id string to ObjectId for database query
            try:
                user_object_id = ObjectId(user_id)
                filter_query["user_id"] = user_object_id
            except Exception:
                raise HTTPException(status_code=400, detail="Invalid user ID format")

        if status:
            filter_query["status"] = status

        # Date filtering
        if start_date or end_date:
            date_filter = {}
            if start_date:
                try:
                    start_dt = datetime.fromisoformat(start_date).replace(tzinfo=timezone.utc)
                    date_filter["$gte"] = start_dt
                except ValueError:
                    raise HTTPException(status_code=400, detail="Invalid start_date format. Use YYYY-MM-DD")

            if end_date:
                try:
                    end_dt = datetime.fromisoformat(end_date).replace(hour=23, minute=59, second=59, tzinfo=timezone.utc)
                    date_filter["$lte"] = end_dt
                except ValueError:
                    raise HTTPException(status_code=400, detail="Invalid end_date format. Use YYYY-MM-DD")

            filter_query["submitted_at"] = date_filter

        # Calculate skip value
        skip = (page - 1) * limit

        # Get total count
        total = await collection.count_documents(filter_query)

        # Get paginated data with sorting
        cursor = collection.find(filter_query).sort(sort_by, sort_order).skip(skip).limit(limit)
        submissions = await cursor.to_list(length=None)

        # Convert ObjectIds to strings
        submissions = convert_object_ids(submissions)

        # Create response models
        submission_responses = [
            SurveySubmissionResponse(**submission) for submission in submissions
        ]

        # Calculate pagination metadata
        total_pages = (total + limit - 1) // limit

        pagination_meta = PaginationMeta(
            page=page,
            limit=limit,
            total=total,
            total_pages=total_pages
        )

        return PaginationResponse[SurveySubmissionResponse](
            data=submission_responses,
            meta=pagination_meta
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting survey submissions: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve survey submissions: {str(e)}")


@router.get("/submissions/{submission_id}", response_model=SurveySubmissionResponse)
async def get_survey_submission(
    submission_id: str,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> SurveySubmissionResponse:
    """
    Get a specific survey submission by ID.

    Args:
        submission_id: Survey submission ID
        user_tenant: User tenant information

    Returns:
        Survey submission details
    """
    try:
        collection = user_tenant.async_db[CollectionName.SURVEY_SUBMISSIONS]

        # Validate ObjectId
        if not ObjectId.is_valid(submission_id):
            raise HTTPException(status_code=400, detail="Invalid submission ID format")

        submission = await collection.find_one({"_id": ObjectId(submission_id)})

        if not submission:
            raise HTTPException(status_code=404, detail="Survey submission not found")

        # Convert ObjectIds to strings
        submission = convert_object_ids(submission)

        return SurveySubmissionResponse(**submission)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting survey submission {submission_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve survey submission: {str(e)}")


@router.post("/submissions", response_model=SurveySubmissionResponse)
async def create_survey_submission(
    submission_data: SurveySubmissionCreate,
    request: Request,
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> SurveySubmissionResponse:
    """
    Create a new survey submission.

    Args:
        submission_data: Survey submission data
        request: FastAPI request object for metadata
        user_tenant: User tenant information

    Returns:
        Created survey submission
    """
    try:
        collection = user_tenant.async_db[CollectionName.SURVEY_SUBMISSIONS]

        # Get client IP and user agent
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")

        # Create submission document
        submission_doc = {
            **submission_data.model_dump(),
            "user_id": ObjectId(user_tenant.user.id),
            "started_at": datetime.now(timezone.utc),
            "submitted_at": datetime.now(timezone.utc) if submission_data.status == SurveySubmissionStatus.COMPLETED else None,
            "last_updated_at": datetime.now(timezone.utc),
            "ip_address": client_ip,
            "user_agent": user_agent
        }

        # Insert the submission
        result = await collection.insert_one(submission_doc)

        # Retrieve the created submission
        created_submission = await collection.find_one({"_id": result.inserted_id})

        # Convert ObjectIds to strings
        created_submission = convert_object_ids(created_submission)

        logger.info(f"Created survey submission {result.inserted_id} by user {user_tenant.user.id}")

        return SurveySubmissionResponse(**created_submission)

    except Exception as e:
        logger.error(f"Error creating survey submission: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create survey submission: {str(e)}")


@router.get("/submissions/user/{user_id}", response_model=PaginationResponse[SurveySubmissionResponse])
async def get_user_survey_submissions(
    user_id: str,
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    status: Optional[SurveySubmissionStatus] = Query(None, description="Filter by status"),
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> PaginationResponse[SurveySubmissionResponse]:
    """
    Get survey submissions for a specific user.

    Args:
        user_id: User ID
        page: Page number
        limit: Number of items per page
        status: Filter by status
        user_tenant: User tenant information

    Returns:
        Paginated list of user's survey submissions
    """
    try:
        # Users can only access their own submissions unless they're admin
        if str(user_tenant.user.id) != user_id and "admin" not in [role.name for role in [user_tenant.user.role]]:
            raise HTTPException(status_code=403, detail="Access denied: You can only view your own submissions")

        collection = user_tenant.async_db[CollectionName.SURVEY_SUBMISSIONS]

        # Build filter query
        # Convert user_id string to ObjectId for database query
        try:
            user_object_id = ObjectId(user_id)
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid user ID format")

        filter_query = {"user_id": user_object_id}

        if status:
            filter_query["status"] = status

        # Calculate skip value
        skip = (page - 1) * limit

        # Get total count
        total = await collection.count_documents(filter_query)

        # Get paginated data with sorting
        cursor = collection.find(filter_query).sort("submitted_at", -1).skip(skip).limit(limit)
        submissions = await cursor.to_list(length=None)

        # Convert ObjectIds to strings
        submissions = convert_object_ids(submissions)

        # Create response models
        submission_responses = [
            SurveySubmissionResponse(**submission) for submission in submissions
        ]

        # Calculate pagination metadata
        total_pages = (total + limit - 1) // limit

        pagination_meta = PaginationMeta(
            page=page,
            limit=limit,
            total=total,
            total_pages=total_pages
        )

        return PaginationResponse[SurveySubmissionResponse](
            data=submission_responses,
            meta=pagination_meta
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user survey submissions: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve user survey submissions: {str(e)}")


@router.delete("/submissions/{submission_id}")
async def delete_survey_submission(
    submission_id: str,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Delete a survey submission.

    Args:
        submission_id: Survey submission ID
        user_tenant: User tenant information

    Returns:
        Success message
    """
    try:
        collection = user_tenant.async_db[CollectionName.SURVEY_SUBMISSIONS]

        # Validate ObjectId
        if not ObjectId.is_valid(submission_id):
            raise HTTPException(status_code=400, detail="Invalid submission ID format")

        # Delete the submission
        result = await collection.delete_one({"_id": ObjectId(submission_id)})

        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Survey submission not found")

        logger.info(f"Deleted survey submission {submission_id} by user {user_tenant.user.id}")

        return {
            "success": True,
            "message": "Survey submission deleted successfully",
            "submission_id": submission_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting survey submission {submission_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete survey submission: {str(e)}")
