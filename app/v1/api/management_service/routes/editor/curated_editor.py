"""
Routes for curated content editor operations.

This module provides comprehensive CRUD operations for curated content management,
including curated sets and individual task items with detailed editing capabilities.
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, Any, List, Optional
from bson import ObjectId
from pymongo.errors import PyMongoError
from datetime import datetime, timezone, timedelta
import asyncio

from app.shared.security import get_tenant_info, require_roles
from app.shared.models.user import UserTenantDB
from app.shared.models.curated_editor import (
    CuratedSetResponse, CuratedSetUpdate, CuratedSetListResponse,
    TaskItemBasic, TaskItemDetailed, TaskItemUpdate, TaskItemListResponse,
    QuestionData, CorrectAnswerData, QuestionEditRequest, QuestionEditResponse, CorrectAnswerEdit,
    CuratedSetDetailsUpdate, CuratedSetDetailsUpdateResponse
)
from app.shared.utils.logger import setup_new_logging
from app.shared.api_response import APIResponse, ResponseMetadata
from app.shared.utils.mongodb import convert_object_ids
from app.v2.api.socket_service_v2.generator.task_utils_v2 import _generate_single_option_audio, _serialize_usage_metadata
from app.v2.api.socket_service_v2.generator.imagen import generate_image
from app.v2.api.socket_service_v2.generator.audiogen import generate_audio

# Configure logging
loggers = setup_new_logging(__name__)

router = APIRouter()


@router.get("/curated-sets", response_model=APIResponse[CuratedSetListResponse])
async def get_curated_sets(
    page: int = Query(1, ge=1, description="Page number (starts from 1)"),
    limit: int = Query(20, ge=1, le=100, description="Number of items per page (max 100)"),
    search: Optional[str] = Query(None, description="Search in set titles"),
    theme_id: Optional[str] = Query(None, description="Filter by theme ID"),
    status: Optional[str] = Query(None, description="Filter by status"),
    gentype: Optional[str] = Query(None, description="Filter by generation type"),
    difficulty_level: Optional[int] = Query(None, ge=1, le=3, description="Filter by difficulty level"),
    sort_by: str = Query("created_at", description="Sort field (created_at, updated_at, title)"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order (asc or desc)"),
    user_tenant: UserTenantDB = Depends(require_roles(["admin", "editor"]))
) -> APIResponse[CuratedSetListResponse]:
    """
    Retrieve all curated sets with advanced filtering and pagination.
    
    This endpoint provides comprehensive access to curated content sets with support for:
    - Pagination with configurable page size
    - Text search in set titles
    - Filtering by theme, status, generation type, and difficulty level
    - Sorting by different fields
    - Total count for pagination metadata
    
    Query Parameters:
    - page: Page number (starts from 1, default: 1)
    - limit: Items per page (1-100, default: 20)
    - search: Optional text search in set titles
    - theme_id: Optional theme ID filter
    - status: Optional status filter (pending, completed, etc.)
    - gentype: Optional generation type filter (primary, secondary, etc.)
    - difficulty_level: Optional difficulty level filter (1-3)
    - sort_by: Sort field (created_at, updated_at, title, default: created_at)
    - sort_order: Sort order (asc/desc, default: desc)
    """
    try:
        # Build filter query
        filter_query = {}
        
        # Theme ID filter
        if theme_id:
            if not ObjectId.is_valid(theme_id):
                raise HTTPException(status_code=400, detail="Invalid theme ID format")
            filter_query["theme_id"] = ObjectId(theme_id)
        
        # Text search filter
        if search:
            filter_query["title"] = {"$regex": search, "$options": "i"}
        
        # Status filter
        if status:
            filter_query["status"] = status
        
        # Generation type filter
        if gentype:
            filter_query["gentype"] = gentype
        
        # Difficulty level filter
        if difficulty_level:
            filter_query["difficulty_level"] = difficulty_level
        
        # Calculate pagination
        skip = (page - 1) * limit
        
        # Validate sort field
        valid_sort_fields = ["created_at", "updated_at", "title", "total_tasks", "status"]
        if sort_by not in valid_sort_fields:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid sort field. Must be one of: {', '.join(valid_sort_fields)}"
            )
        
        # Set sort direction
        sort_direction = 1 if sort_order == "asc" else -1
        
        # Get total count for pagination metadata
        total_count = await user_tenant.async_db.curated_content_set.count_documents(filter_query)
        
        # Calculate pagination metadata
        total_pages = (total_count + limit - 1) // limit
        has_next = page < total_pages
        has_prev = page > 1
        
        # Get curated sets from database with pagination and sorting
        sets_cursor = user_tenant.async_db.curated_content_set.find(filter_query).sort(
            sort_by, sort_direction
        ).skip(skip).limit(limit)
        
        sets = await sets_cursor.to_list(length=None)
        
        # Convert ObjectIds to strings and format response
        formatted_sets = []
        for set_data in sets:
            set_data = convert_object_ids(set_data)
            formatted_sets.append(CuratedSetResponse(**set_data))
        
        # Build response data
        response_data = CuratedSetListResponse(
            sets=formatted_sets,
            pagination={
                "current_page": page,
                "total_pages": total_pages,
                "total_items": total_count,
                "items_per_page": limit,
                "has_next": has_next,
                "has_prev": has_prev
            },
            filters={
                "search": search,
                "theme_id": theme_id,
                "status": status,
                "gentype": gentype,
                "difficulty_level": difficulty_level,
                "sort_by": sort_by,
                "sort_order": sort_order
            }
        )
        
        # Generate appropriate message
        message_parts = []
        if search:
            message_parts.append(f"matching '{search}'")
        if theme_id:
            message_parts.append(f"for theme {theme_id}")
        if status:
            message_parts.append(f"with status '{status}'")
        
        base_message = "Curated sets retrieved successfully"
        if message_parts:
            message = f"{base_message} {' '.join(message_parts)}"
        else:
            message = base_message
        
        return APIResponse[CuratedSetListResponse](
            success=True,
            data=response_data,
            message=message,
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )
        
    except HTTPException:
        raise
    except PyMongoError as e:
        loggers.error(f"Database error in get_curated_sets: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in get_curated_sets: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.get("/curated-sets/{set_id}", response_model=APIResponse[CuratedSetResponse])
async def get_curated_set(
    set_id: str,
    user_tenant: UserTenantDB = Depends(require_roles(["admin", "editor"]))
) -> APIResponse[CuratedSetResponse]:
    """
    Retrieve a specific curated set by ID.
    
    This endpoint returns detailed information about a single curated set,
    including all metadata and statistics.
    
    Path Parameters:
    - set_id: The ID of the curated set to retrieve
    """
    try:
        # Validate set_id
        if not ObjectId.is_valid(set_id):
            raise HTTPException(status_code=400, detail="Invalid set ID format")
        
        # Get curated set from database
        set_data = await user_tenant.async_db.curated_content_set.find_one(
            {"_id": ObjectId(set_id)}
        )
        
        if not set_data:
            raise HTTPException(status_code=404, detail="Curated set not found")
        # regenreate thumbnaile_metadata url
        if "thumbnail_metadata" in set_data and set_data["thumbnail_metadata"]:
            try:
                presigned_url = user_tenant.minio.get_presigned_url(
                    bucket_name=user_tenant.minio_bucket_name,
                    object_name=set_data["thumbnail_metadata"].get("object_name"),
                    expires=timedelta(hours=24),
                    method="GET"
                )
                set_data["thumbnail_metadata"]["url"] = presigned_url
            except Exception as e:
                loggers.error(f"Error generating thumbnail metadata URL for {set_data['thumbnail_metadata']['object_name']}: {e}")
                set_data["thumbnail_metadata"]["url"] = None
        # Convert ObjectIds to strings and format response
        set_data = convert_object_ids(set_data)
        curated_set = CuratedSetResponse(**set_data)
        
        return APIResponse[CuratedSetResponse](
            success=True,
            data=curated_set,
            message="Curated set retrieved successfully",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )
        
    except HTTPException:
        raise
    except PyMongoError as e:
        loggers.error(f"Database error in get_curated_set: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in get_curated_set: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.put("/curated-sets/{set_id}", response_model=APIResponse[CuratedSetResponse])
async def update_curated_set(
    set_id: str,
    update_data: CuratedSetUpdate,
    user_tenant: UserTenantDB = Depends(require_roles(["admin", "editor"]))
) -> APIResponse[CuratedSetResponse]:
    """
    Edit/update a specific curated set.

    This endpoint allows updating curated set properties such as title,
    status, difficulty level, and metadata.

    Path Parameters:
    - set_id: The ID of the curated set to update

    Request Body:
    - title: Optional new title for the set
    - status: Optional new status
    - difficulty_level: Optional new difficulty level (1-3)
    - metadata: Optional additional metadata
    """
    try:
        # Validate set_id
        if not ObjectId.is_valid(set_id):
            raise HTTPException(status_code=400, detail="Invalid set ID format")

        # Check if set exists
        existing_set = await user_tenant.async_db.curated_content_set.find_one(
            {"_id": ObjectId(set_id)}
        )

        if not existing_set:
            raise HTTPException(status_code=404, detail="Curated set not found")

        # Build update document with only provided fields
        update_doc = {"updated_at": datetime.now(timezone.utc)}

        # Update fields if provided
        if update_data.title is not None:
            update_doc["title"] = update_data.title
        if update_data.title_en is not None:
            update_doc["title_en"] = update_data.title_en
        if update_data.description is not None:
            update_doc["description"] = update_data.description
        if update_data.description_en is not None:
            update_doc["description_en"] = update_data.description_en
        if update_data.status is not None:
            update_doc["status"] = update_data.status
        if update_data.difficulty_level is not None:
            update_doc["difficulty_level"] = update_data.difficulty_level
        if update_data.metadata is not None:
            update_doc["metadata"] = update_data.metadata
        if update_data.thumbnail is not None:
            update_doc["thumbnail"] = update_data.thumbnail
            # generate image from the   thumbnail keyword
            try:
                _file_text, file_info, usage_metadata = await generate_image(user_tenant, update_data.thumbnail)
                update_doc["thumbnail_metadata"] = file_info
               
            except Exception as e:
                loggers.error(f"Error generating thumbnail metadata URL: {e}")
                raise HTTPException(status_code=500, detail="Failed to generate thumbnail metadata URL")
        # Check if at least one field is being updated
        if len(update_doc) == 1:  # Only updated_at
            raise HTTPException(
                status_code=400,
                detail="At least one field must be provided for update"
            )

        # Update the set
        await user_tenant.async_db.curated_content_set.update_one(
            {"_id": ObjectId(set_id)},
            {"$set": update_doc}
        )

        # Get the updated set
        updated_set = await user_tenant.async_db.curated_content_set.find_one(
            {"_id": ObjectId(set_id)}
        )

        # Convert ObjectIds to strings and format response
        updated_set = convert_object_ids(updated_set)
        curated_set = CuratedSetResponse(**updated_set)

        return APIResponse[CuratedSetResponse](
            success=True,
            data=curated_set,
            message="Curated set updated successfully",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )

    except HTTPException:
        raise
    except PyMongoError as e:
        loggers.error(f"Database error in update_curated_set: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in update_curated_set: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.get("/curated-sets/{set_id}/tasks", response_model=APIResponse[TaskItemListResponse])
async def get_curated_set_tasks(
    set_id: str,
    user_tenant: UserTenantDB = Depends(require_roles(["admin", "editor"]))
) -> APIResponse[TaskItemListResponse]:
    """
    Get all task items within a curated set.

    This endpoint returns basic information about all task items in a curated set,
    including task IDs and essential properties for listing and navigation.

    Path Parameters:
    - set_id: The ID of the curated set to get tasks for
    """
    try:
        # Validate set_id
        if not ObjectId.is_valid(set_id):
            raise HTTPException(status_code=400, detail="Invalid set ID format")

        # Get curated set from database
        set_data = await user_tenant.async_db.curated_content_set.find_one(
            {"_id": ObjectId(set_id)},
            {"title": 1, "tasks": 1, "total_tasks": 1, "status": 1, "difficulty_level": 1}
        )

        if not set_data:
            raise HTTPException(status_code=404, detail="Curated set not found")

        # Get task IDs from the set
        task_ids = set_data.get("tasks", [])

        if not task_ids:
            # Return empty response if no tasks
            response_data = TaskItemListResponse(
                tasks=[],
                set_info=convert_object_ids(set_data),
                total_count=0
            )

            return APIResponse[TaskItemListResponse](
                success=True,
                data=response_data,
                message="No tasks found in curated set",
                metadata=ResponseMetadata(
                    timestamp=None,
                    request_id=None
                )
            )

        # Convert string IDs to ObjectIds for query
        task_object_ids = [ObjectId(tid) for tid in task_ids if ObjectId.is_valid(tid)]

        # Get basic task information
        tasks_cursor = user_tenant.async_db.curated_content_items.find(
            {"_id": {"$in": task_object_ids}},
            {
                "_id": 1,
                "type": 1,
                "title": 1,
                "status": 1,
                "difficulty_level": 1,
                "total_score": 1
            }
        )

        tasks = await tasks_cursor.to_list(length=None)

        # Format task data
        formatted_tasks = []
        for task in tasks:
            task = convert_object_ids(task)
            formatted_tasks.append(TaskItemBasic(**task))

        # Build response data
        response_data = TaskItemListResponse(
            tasks=formatted_tasks,
            set_info=convert_object_ids(set_data),
            total_count=len(formatted_tasks)
        )

        return APIResponse[TaskItemListResponse](
            success=True,
            data=response_data,
            message=f"Retrieved {len(formatted_tasks)} tasks from curated set",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )

    except HTTPException:
        raise
    except PyMongoError as e:
        loggers.error(f"Database error in get_curated_set_tasks: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in get_curated_set_tasks: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.get("/curated-tasks/{task_id}", response_model=APIResponse[TaskItemDetailed])
async def get_curated_task(
    task_id: str,
    user_tenant: UserTenantDB = Depends(require_roles(["admin", "editor"]))
) -> APIResponse[TaskItemDetailed]:
    """
    Fetch a specific curated task item by its ID with all details.

    This endpoint returns comprehensive information about a single task item,
    including questions, options, options_en, correct answers, and all metadata.
    The response is structured to facilitate editing of individual components.

    Path Parameters:
    - task_id: The ID of the task item to retrieve
    """
    try:
        # Validate task_id
        if not ObjectId.is_valid(task_id):
            raise HTTPException(status_code=400, detail="Invalid task ID format")

        # Get task item from database
        task_data = await user_tenant.async_db.curated_content_items.find_one(
            {"_id": ObjectId(task_id)}
        )

        if not task_data:
            raise HTTPException(status_code=404, detail="Task item not found")

        # Convert ObjectIds to strings
        task_data = convert_object_ids(task_data)

        # Refresh presigned URLs for metadata and options_metadata in parallel
        async def refresh_metadata_url():
            """Refresh presigned URL for metadata (image_metadata)."""
            question_data = task_data.get("question", {})
            image_metadata = question_data.get("metadata", {})
            if image_metadata and "object_name" in image_metadata:
                try:
                    presigned_url = user_tenant.minio.get_presigned_url(
                        bucket_name=user_tenant.minio_bucket_name,
                        object_name=image_metadata["object_name"],
                        expires=timedelta(hours=24),
                        method="GET"
                    )
                    image_metadata["url"] = presigned_url
                    loggers.debug(f"✅ Refreshed image metadata URL for task {task_id}")
                except Exception as e:
                    loggers.error(f"❌ Error refreshing image metadata URL for task {task_id}: {e}")


        async def refresh_options_metadata_urls():
            """Refresh presigned URLs for all options_metadata in parallel."""
            question_data = task_data.get("question", {})
            options_metadata = question_data.get("options_metadata", {})

            if options_metadata:
                loggers.info(f"🔊 Refreshing presigned URLs for {len(options_metadata)} options in task {task_id}")

                async def refresh_single_option_url(option_key: str, option_data: dict):
                    """Refresh presigned URL for a single option's audio."""
                    try:
                        if "file_info" in option_data and "object_name" in option_data["file_info"]:
                            object_name = option_data["file_info"]["object_name"]

                            # Generate new presigned URL
                            presigned_url = user_tenant.minio.get_presigned_url(
                                bucket_name=user_tenant.minio_bucket_name,
                                object_name=object_name,
                                expires=timedelta(hours=24),
                                method="GET"
                            )

                            # Update the audio_url with fresh presigned URL
                            option_data["audio_url"] = presigned_url
                            loggers.debug(f"✅ Refreshed audio URL for option {option_key}")

                    except Exception as e:
                        loggers.error(f"❌ Error refreshing audio URL for option {option_key}: {e}")
                        # Keep existing URL if refresh fails

                # Refresh URLs for all options in parallel
                refresh_tasks = [
                    refresh_single_option_url(option_key, option_data)
                    for option_key, option_data in options_metadata.items()
                    if isinstance(option_data, dict)
                ]

                if refresh_tasks:
                    await asyncio.gather(*refresh_tasks, return_exceptions=True)
                    loggers.debug(f"🔊 Completed presigned URL refresh for options in task {task_id}")
            else:
                loggers.debug(f"📝 Task {task_id} has no options_metadata")

        # Execute all URL refresh operations in parallel
        await asyncio.gather(
            refresh_metadata_url(),
            refresh_options_metadata_urls(),
            return_exceptions=True
        )

        # Extract and structure question data
        question_raw = task_data.get("question", {})
        question_data = QuestionData(
            text=question_raw.get("text", ""),
            translated_text=question_raw.get("translated_text"),
            options=question_raw.get("options", []),
            options_en=question_raw.get("options_en"),
            correct_answer_index=question_raw.get("correct_answer_index", 0),
            audio_metadata=question_raw.get("audio_metadata"),
            image_metadata=question_raw.get("image_metadata"),
            metadata=question_raw.get("metadata"),
            answer_hint=question_raw.get("answer_hint"),
            options_metadata=question_raw.get("options_metadata")
        )

        # Extract and structure correct answer data
        correct_answer_raw = task_data.get("correct_answer", {})
        correct_answer_data = CorrectAnswerData(**correct_answer_raw)

        # Build detailed task item response
        task_item = TaskItemDetailed(
            id=task_data.get("id", task_data.get("_id")),
            type=task_data.get("type", ""),
            title=task_data.get("title", ""),
            question=question_data,
            correct_answer=correct_answer_data,
            status=task_data.get("status", "pending"),
            difficulty_level=task_data.get("difficulty_level", 1),
            total_score=task_data.get("total_score", 10),
            metadata=task_data.get("metadata", {}),
            created_at=task_data.get("created_at"),
            updated_at=task_data.get("updated_at")
        )

        return APIResponse[TaskItemDetailed](
            success=True,
            data=task_item,
            message="Task item retrieved successfully",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )

    except HTTPException:
        raise
    except PyMongoError as e:
        loggers.error(f"Database error in get_curated_task: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in get_curated_task: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")





@router.put("/curated-tasks/{task_id}/question", response_model=APIResponse[QuestionEditResponse])
async def edit_task_question(
    task_id: str,
    edit_data: QuestionEditRequest,
    user_tenant: UserTenantDB = Depends(require_roles(["admin", "editor"]))
) -> APIResponse[QuestionEditResponse]:
    """
    Edit question text, translated text, options, options_en, answer hint, and correct answer with automatic audio regeneration.

    This endpoint allows comprehensive editing of question components including:
    - Question text (Nepali and English)
    - Answer options (Nepali and English)
    - Answer hint
    - Correct answer (supports all task types: single_choice, multiple_choice, image_identification)
    - Automatic audio regeneration for options using PiperTTS

    Path Parameters:
    - task_id: The ID of the task item to edit

    Request Body:
    - text: Optional question text in Nepali
    - translated_text: Optional question text in English
    - options: Optional answer options in Nepali (Dict[str, str])
    - options_en: Optional answer options in English (Dict[str, str])
    - answer_hint: Optional answer hint text
    - correct_answer: Optional correct answer data with type and value
    - regenerate_audio: Whether to regenerate audio for options (default: true)
    """
    try:
        # Validate task_id
        if not ObjectId.is_valid(task_id):
            raise HTTPException(status_code=400, detail="Invalid task ID format")

        # Check if task exists
        existing_task = await user_tenant.async_db.curated_content_items.find_one(
            {"_id": ObjectId(task_id)}
        )

        if not existing_task:
            raise HTTPException(status_code=404, detail="Task item not found")

        # Build update document
        update_doc = {"updated_at": datetime.now(timezone.utc)}
        updated_fields = []

        # Update question text fields
        if edit_data.text is not None:
            update_doc["question.text"] = edit_data.text
            updated_fields.append("text")

        if edit_data.translated_text is not None:
            update_doc["question.translated_text"] = edit_data.translated_text
            updated_fields.append("translated_text")

        # Update options
        if edit_data.options is not None:
            update_doc["question.options"] = edit_data.options
            updated_fields.append("options")

        if edit_data.options_en is not None:
            update_doc["question.options_en"] = edit_data.options_en
            updated_fields.append("options_en")

        # Update answer hint
        if edit_data.answer_hint is not None:
            update_doc["question.answer_hint"] = edit_data.answer_hint
            updated_fields.append("answer_hint")

        # Update correct answer
        correct_answer_updated = False
        if edit_data.correct_answer is not None:
            # Validate correct answer based on type
            correct_answer_data = edit_data.correct_answer

            if correct_answer_data.type == "single_choice":
                # For single choice, value should be a string (e.g., "a", "b", "c", "d")
                if not isinstance(correct_answer_data.value, str):
                    raise HTTPException(
                        status_code=400,
                        detail="For single_choice tasks, correct_answer.value must be a string"
                    )
            elif correct_answer_data.type == "multiple_choice":
                # For multiple choice, value should be a list of strings (e.g., ["a", "b"])
                if not isinstance(correct_answer_data.value, list):
                    raise HTTPException(
                        status_code=400,
                        detail="For multiple_choice tasks, correct_answer.value must be a list"
                    )
            elif correct_answer_data.type == "image_identification":
                # For image identification, value should be a string (e.g., "a")
                if not isinstance(correct_answer_data.value, str):
                    raise HTTPException(
                        status_code=400,
                        detail="For image_identification tasks, correct_answer.value must be a string"
                    )

            # Update correct answer
            update_doc["correct_answer"] = {
                "type": correct_answer_data.type,
                "value": correct_answer_data.value
            }
            updated_fields.append("correct_answer")
            correct_answer_updated = True

        # Check if at least one field is being updated
        if len(updated_fields) == 0:
            raise HTTPException(
                status_code=400,
                detail="At least one field (text, translated_text, options, options_en, answer_hint, or correct_answer) must be provided for update"
            )

        # Regenerate audio for options if requested and options were updated
        options_metadata = {}
        audio_regenerated = False

        if edit_data.regenerate_audio and edit_data.options is not None:
            loggers.info(f"🔊 TASK {task_id} | REGENERATING OPTIONS AUDIO")

            options = edit_data.options
            total_options = len(options)

            # Process each option sequentially
            for i, (option_key, option_value) in enumerate(options.items(), 1):
                try:
                    # Generate audio for this option
                    audio_url, file_info = await _generate_single_option_audio(
                        user_tenant, str(option_value), ObjectId(task_id), option_key, i, total_options
                    )

                    options_metadata[option_key] = {
                        "text": str(option_value),
                        "audio_url": audio_url,
                        "file_info": file_info,
                        "cache_id": f"{str(option_value).strip().lower()}_audio",
                        "generated_at": datetime.now(timezone.utc).isoformat()
                    }

                except Exception as e:
                    loggers.error(f"❌ TASK {task_id} | OPTION {option_key} AUDIO GENERATION FAILED: {e}")
                    # Continue with other options even if one fails
                    options_metadata[option_key] = {
                        "text": str(option_value),
                        "audio_url": "",
                        "file_info": {},
                        "cache_id": f"{str(option_value).strip().lower()}_audio",
                        "generated_at": datetime.now(timezone.utc).isoformat(),
                        "error": str(e)
                    }

            # Update options metadata
            if options_metadata:
                update_doc["question.options_metadata"] = options_metadata
                update_doc["metadata._options_audio_ready"] = True
                update_doc["metadata.options_audio_status"] = "completed"
                audio_regenerated = True
                loggers.info(f"✅ TASK {task_id} | OPTIONS AUDIO REGENERATED: {len(options_metadata)} options")

        # Generate hint media for image_identification and speak_word tasks
        hint_media_generated = False
        hint_media_type = None
        question_metadata = {}

        if edit_data.answer_hint is not None:
            task_type = existing_task.get("type", "")
            answer_hint = edit_data.answer_hint

            if task_type == "image_identification" and answer_hint.strip():
                # Generate image for image_identification tasks
                try:
                    loggers.info(f"🎨 TASK {task_id} | GENERATING HINT IMAGE for: {answer_hint}")
                    _file_text, file_info, usage_metadata = await generate_image(user_tenant, answer_hint)

                    if file_info:
                        question_metadata = {
                            **file_info,
                            "hint_text": answer_hint,
                            "media_type": "image",
                            "generated_at": datetime.now(timezone.utc).isoformat(),
                            "usage": _serialize_usage_metadata(usage_metadata)
                        }
                        update_doc["question.metadata"] = question_metadata
                        update_doc["metadata._hint_image_ready"] = True
                        update_doc["metadata.hint_image_status"] = "completed"
                        hint_media_generated = True
                        hint_media_type = "image"
                        loggers.info(f"✅ TASK {task_id} | HINT IMAGE GENERATED for: {answer_hint}")
                    else:
                        loggers.error(f"❌ TASK {task_id} | HINT IMAGE GENERATION FAILED for: {answer_hint}")

                except Exception as e:
                    loggers.error(f"❌ TASK {task_id} | HINT IMAGE GENERATION ERROR: {e}")

            elif task_type == "speak_word" and answer_hint.strip():
                # Generate audio for speak_word tasks
                try:
                    loggers.info(f"🔊 TASK {task_id} | GENERATING HINT AUDIO for: {answer_hint}")
                    _file_text, file_info, usage_metadata = await generate_audio(user_tenant, answer_hint, "audio_prompt", "edgetts")

                    if file_info:
                        question_metadata = {
                            **file_info,
                            "hint_text": answer_hint,
                            "media_type": "audio",
                            "generated_at": datetime.now(timezone.utc).isoformat(),
                            "usage": _serialize_usage_metadata(usage_metadata)
                        }
                        update_doc["question.metadata"] = question_metadata
                        update_doc["metadata._hint_audio_ready"] = True
                        update_doc["metadata.hint_audio_status"] = "completed"
                        hint_media_generated = True
                        hint_media_type = "audio"
                        loggers.info(f"✅ TASK {task_id} | HINT AUDIO GENERATED for: {answer_hint}")
                    else:
                        loggers.error(f"❌ TASK {task_id} | HINT AUDIO GENERATION FAILED for: {answer_hint}")

                except Exception as e:
                    loggers.error(f"❌ TASK {task_id} | HINT AUDIO GENERATION ERROR: {e}")

        # Update the task
        await user_tenant.async_db.curated_content_items.update_one(
            {"_id": ObjectId(task_id)},
            {"$set": update_doc}
        )

        # Build response
        response_data = QuestionEditResponse(
            task_id=task_id,
            updated_fields=updated_fields,
            audio_regenerated=audio_regenerated,
            options_metadata=options_metadata if audio_regenerated else None,
            correct_answer_updated=correct_answer_updated,
            hint_media_generated=hint_media_generated,
            hint_media_type=hint_media_type,
            question_metadata=question_metadata if hint_media_generated else None,
            message=f"Question updated successfully. Fields updated: {', '.join(updated_fields)}"
        )

        return APIResponse[QuestionEditResponse](
            success=True,
            data=response_data,
            message="Question edited successfully",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )

    except HTTPException:
        raise
    except PyMongoError as e:
        loggers.error(f"Database error in edit_task_question: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in edit_task_question: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.put("/curated-sets/{set_id}/details", response_model=APIResponse[CuratedSetDetailsUpdateResponse])
async def update_curated_set_details(
    set_id: str,
    update_data: CuratedSetDetailsUpdate,
    user_tenant: UserTenantDB = Depends(require_roles(["admin", "editor"]))
) -> APIResponse[CuratedSetDetailsUpdateResponse]:
    """
    Update curated set details including title, title_en, description, description_en, and thumbnail.

    This endpoint allows updating the basic details of a curated set. If a thumbnail keyword
    is provided and regenerate_thumbnail is True, it will generate a new thumbnail image.

    Path Parameters:
    - set_id: The ID of the curated set to update

    Request Body:
    - title: Set title in Nepali (optional)
    - title_en: Set title in English (optional)
    - description: Set description in Nepali (optional)
    - description_en: Set description in English (optional)
    - thumbnail: Thumbnail keyword for image generation (optional)
    - regenerate_thumbnail: Whether to regenerate thumbnail image (default: False)
    """
    try:
        # Validate set_id
        if not ObjectId.is_valid(set_id):
            raise HTTPException(status_code=400, detail="Invalid set ID format")

        # Check if set exists
        existing_set = await user_tenant.async_db.curated_content_set.find_one(
            {"_id": ObjectId(set_id)}
        )

        if not existing_set:
            raise HTTPException(status_code=404, detail="Curated set not found")

        # Build update document
        update_doc = {
            "updated_at": datetime.now(timezone.utc)
        }

        updated_fields = []
        thumbnail_regenerated = False
        thumbnail_metadata = None

        # Update title fields
        if update_data.title is not None:
            update_doc["title"] = update_data.title
            updated_fields.append("title")

        if update_data.title_en is not None:
            update_doc["title_en"] = update_data.title_en
            updated_fields.append("title_en")

        # Update description fields
        if update_data.description is not None:
            update_doc["description"] = update_data.description
            updated_fields.append("description")

        if update_data.description_en is not None:
            update_doc["description_en"] = update_data.description_en
            updated_fields.append("description_en")

        # Handle thumbnail update and regeneration
        if update_data.thumbnail is not None:
            update_doc["thumbnail"] = update_data.thumbnail
            updated_fields.append("thumbnail")

            # Regenerate thumbnail image if requested
            if update_data.regenerate_thumbnail:
                try:
                    loggers.info(f"🎨 SET {set_id} | GENERATING THUMBNAIL IMAGE for: {update_data.thumbnail}")
                    _file_text, file_info, usage_metadata = await generate_image(user_tenant, update_data.thumbnail)

                    if file_info:
                        thumbnail_metadata = {
                            **file_info,
                            "keyword": update_data.thumbnail,
                            "media_type": "image",
                            "generated_at": datetime.now(timezone.utc).isoformat(),
                            "usage": _serialize_usage_metadata(usage_metadata)
                        }
                        update_doc["thumbnail_metadata"] = thumbnail_metadata
                        thumbnail_regenerated = True
                        updated_fields.append("thumbnail_metadata")
                        loggers.info(f"✅ SET {set_id} | THUMBNAIL IMAGE GENERATED for: {update_data.thumbnail}")
                    else:
                        loggers.error(f"❌ SET {set_id} | THUMBNAIL IMAGE GENERATION FAILED for: {update_data.thumbnail}")

                except Exception as e:
                    loggers.error(f"❌ SET {set_id} | THUMBNAIL IMAGE GENERATION ERROR: {e}")

        # Update the curated set
        if updated_fields:
            await user_tenant.async_db.curated_content_set.update_one(
                {"_id": ObjectId(set_id)},
                {"$set": update_doc}
            )
            loggers.info(f"✅ SET {set_id} | UPDATED FIELDS: {', '.join(updated_fields)}")
        else:
            loggers.info(f"📝 SET {set_id} | NO FIELDS TO UPDATE")

        # Build response
        response_data = CuratedSetDetailsUpdateResponse(
            set_id=set_id,
            updated_fields=updated_fields,
            thumbnail_regenerated=thumbnail_regenerated,
            thumbnail_metadata=thumbnail_metadata,
            message=f"Curated set details updated successfully. Fields updated: {', '.join(updated_fields) if updated_fields else 'none'}"
        )

        return APIResponse[CuratedSetDetailsUpdateResponse](
            success=True,
            data=response_data,
            message="Curated set details updated successfully",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )

    except HTTPException:
        raise
    except PyMongoError as e:
        loggers.error(f"Database error in update_curated_set_details: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in update_curated_set_details: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")
