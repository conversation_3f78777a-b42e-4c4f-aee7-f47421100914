"""
Admin dashboard routes for the Management Service.

This module provides admin-only endpoints for:
- Dashboard metrics and analytics
- User detail information
- System statistics
"""

from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query
from bson import ObjectId

from app.shared.models.user import UserTenantDB
from app.shared.security import require_roles
from app.shared.utils.logger import setup_new_logging

# Configure logging
loggers = setup_new_logging(__name__)

router = APIRouter()



@router.get("/admin/dashboard/user/{user_id}")
async def get_user_details(
    user_id: str,
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get detailed information about a specific user.
    
    Args:
        user_id: The ID of the user to get details for
        
    Returns:
        Dictionary containing comprehensive user information including:
        - Basic user info (username, role, join date, etc.)
        - Task activity (total tasks, completed tasks, scores)
        - Recent activity timeline
        - Performance metrics
    """
    try:
        loggers.info(f"Getting detailed info for user: {user_id}")
        
        # Validate user_id format
        try:
            user_object_id = ObjectId(user_id)
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid user ID format")
        
        # Get database collections
        users_collection = user_tenant.async_db.users
        task_sets_collection = user_tenant.async_db.task_sets
        task_items_collection = user_tenant.async_db.task_items
        
        # Get user basic info
        user = await users_collection.find_one({"_id": user_object_id})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # User task activity aggregation
        user_activity_pipeline = [
            {"$match": {"user_id": user_id}},
            {
                "$facet": {
                    "task_sets_summary": [
                        {"$group": {
                            "_id": None,
                            "total_task_sets": {"$sum": 1},
                            "completed_task_sets": {
                                "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                            },
                            "total_score_earned": {"$sum": "$scored"},
                            "total_possible_score": {"$sum": "$total_score"},
                            "avg_score": {"$avg": "$scored"}
                        }}
                    ],
                    "task_sets_by_status": [
                        {"$group": {"_id": "$status", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "task_sets_by_type": [
                        {"$group": {"_id": "$input_type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}}
                    ],
                    "recent_activity": [
                        {"$sort": {"created_at": -1}},
                        {"$limit": 10},
                        {"$project": {
                            "_id": 1,
                            "input_type": 1,
                            "status": 1,
                            "created_at": 1,
                            "completed_at": 1,
                            "scored": 1,
                            "total_score": 1,
                            "total_tasks": 1
                        }}
                    ]
                }
            }
        ]
        
        # Execute user activity aggregation
        activity_results = await task_sets_collection.aggregate(user_activity_pipeline).to_list(1)
        activity_data = activity_results[0] if activity_results else {}
        
        # Get task items created by this user (if any)
        user_created_tasks = await task_items_collection.count_documents({"created_by": user_id})
        
        # Build user details response
        user_details = {
            "user_info": {
                "id": str(user["_id"]),
                "username": user.get("username"),
                "role": user.get("role"),
                "email": user.get("email"),
                "full_name": user.get("full_name"),
                "created_at": user.get("created_at"),
                "last_login": user.get("last_login"),
                "previous_login": user.get("previous_login"),
                "onboarding_completed": user.get("onboarding_completed", False),
                "age": user.get("age"),
                "difficulty_level": user.get("difficulty_level"),
                "preferred_topics": user.get("preferred_topics", [])
            },
            "activity_summary": activity_data.get("task_sets_summary", [{}])[0] if activity_data.get("task_sets_summary") else {},
            "task_distribution": {
                "by_status": activity_data.get("task_sets_by_status", []),
                "by_type": activity_data.get("task_sets_by_type", [])
            },
            "recent_activity": activity_data.get("recent_activity", []),
            "content_creation": {
                "tasks_created": user_created_tasks
            }
        }
        
        # Convert ObjectIds to strings in recent activity
        for activity in user_details["recent_activity"]:
            activity["_id"] = str(activity["_id"])
        
        loggers.info(f"User details retrieved successfully for user: {user_id}")
        return user_details
        
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting user details for {user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve user details: {str(e)}")


@router.get("/dashboard")
async def get_dashboard_overview(
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get basic dashboard overview metrics.

    Returns:
        Dictionary containing basic overview metrics
    """
    try:
        loggers.info("Getting dashboard overview")

        # Get current date
        now = datetime.now(timezone.utc)
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

        # Get database collections
        users_collection = user_tenant.async_db.users
        task_sets_collection = user_tenant.async_db.task_sets

        # Basic counts
        total_users = await users_collection.count_documents({})
        users_joined_today = await users_collection.count_documents({"created_at": {"$gte": today_start}})
        users_active_today = await users_collection.count_documents({"last_login": {"$gte": today_start}})

        total_task_sets = await task_sets_collection.count_documents({})
        task_sets_created_today = await task_sets_collection.count_documents({"created_at": {"$gte": today_start}})
        task_sets_completed_today = await task_sets_collection.count_documents({
            "completed_at": {"$gte": today_start},
            "status": "completed"
        })

        return {
            "timestamp": now.isoformat(),
            "overview": {
                "total_users": total_users,
                "users_joined_today": users_joined_today,
                "users_active_today": users_active_today,
                "total_task_sets": total_task_sets,
                "task_sets_created_today": task_sets_created_today,
                "task_sets_completed_today": task_sets_completed_today
            }
        }

    except Exception as e:
        loggers.error(f"Error getting dashboard overview: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve dashboard overview: {str(e)}")


@router.get("/dashboard/users")
async def get_users_metrics(
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD), defaults to 7 days ago"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD), defaults to today"),
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get user metrics for date range with daily breakdown for graphs.
    Default range is past 7 days.

    Returns:
        Dictionary containing user metrics with daily breakdown
    """
    try:
        loggers.info(f"Getting user metrics from {start_date} to {end_date}")

        # Parse dates or use defaults
        now = datetime.now(timezone.utc)

        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            end_dt = end_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
        else:
            end_dt = now

        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        else:
            start_dt = end_dt - timedelta(days=7)

        # Get database collection
        users_collection = user_tenant.async_db.users

        # Daily user registrations pipeline
        daily_registrations_pipeline = [
            {
                "$match": {
                    "created_at": {"$gte": start_dt, "$lte": end_dt}
                }
            },
            {
                "$group": {
                    "_id": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": "$created_at"
                        }
                    },
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"_id": 1}}
        ]

        # Daily active users pipeline
        daily_active_pipeline = [
            {
                "$match": {
                    "last_login": {"$gte": start_dt, "$lte": end_dt}
                }
            },
            {
                "$group": {
                    "_id": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": "$last_login"
                        }
                    },
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"_id": 1}}
        ]

        # Users by role pipeline
        users_by_role_pipeline = [
            {
                "$match": {
                    "created_at": {"$gte": start_dt, "$lte": end_dt}
                }
            },
            {
                "$group": {
                    "_id": "$role",
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"count": -1}}
        ]

        # Execute aggregations
        daily_registrations = await (await users_collection.aggregate(daily_registrations_pipeline)).to_list(None)
        daily_active = await (await users_collection.aggregate(daily_active_pipeline)).to_list(None)
        users_by_role = await (await users_collection.aggregate(users_by_role_pipeline)).to_list(None)

        # Get total counts
        total_users = await users_collection.count_documents({})
        users_in_period = await users_collection.count_documents({
            "created_at": {"$gte": start_dt, "$lte": end_dt}
        })

        return {
            "timestamp": now.isoformat(),
            "date_range": {
                "start_date": start_dt.strftime("%Y-%m-%d"),
                "end_date": end_dt.strftime("%Y-%m-%d")
            },
            "summary": {
                "total_users": total_users,
                "users_in_period": users_in_period
            },
            "daily_data": {
                "registrations": daily_registrations,
                "active_users": daily_active
            },
            "distribution": {
                "by_role": users_by_role
            }
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format. Use YYYY-MM-DD: {str(e)}")
    except Exception as e:
        loggers.error(f"Error getting user metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve user metrics: {str(e)}")


@router.get("/dashboard/task-sets")
async def get_task_sets_metrics(
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD), defaults to today"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD), defaults to today"),
    user_tenant: UserTenantDB = Depends(require_roles(["admin"]))
) -> Dict[str, Any]:
    """
    Get task sets metrics for date range with task type differentiation.
    Default is today's date for both start and end.

    Returns:
        Dictionary containing task sets metrics including primary/followup/curated breakdown
    """
    try:
        loggers.info(f"Getting task sets metrics from {start_date} to {end_date}")

        # Parse dates or use today
        now = datetime.now(timezone.utc)

        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            start_dt = start_dt.replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            start_dt = now.replace(hour=0, minute=0, second=0, microsecond=0)

        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            end_dt = end_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
        else:
            end_dt = now.replace(hour=23, minute=59, second=59, microsecond=999999)

        # Get database collections
        task_sets_collection = user_tenant.async_db.task_sets
        task_items_collection = user_tenant.async_db.task_items

        # Date-wise hierarchical pipeline for gentype breakdown
        task_sets_pipeline = [
            # Stage 1: Match task sets in date range
            {
                "$match": {
                    "created_at": {"$gte": start_dt, "$lte": end_dt}
                }
            },

            # Stage 2: Add date field for grouping by day
            {
                "$addFields": {
                    "date_only": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": "$created_at"
                        }
                    }
                }
            },

            # Stage 3: Convert task IDs to ObjectIds and lookup task items
            {
                "$addFields": {
                    "task_object_ids": {
                        "$cond": [
                            {"$and": [
                                {"$ne": ["$tasks", None]},
                                {"$ne": ["$tasks", []]},
                                {"$isArray": "$tasks"}
                            ]},
                            {
                                "$map": {
                                    "input": "$tasks",
                                    "as": "task_id",
                                    "in": {
                                        "$cond": [
                                            {"$ne": ["$$task_id", None]},
                                            {"$toObjectId": "$$task_id"},
                                            None
                                        ]
                                    }
                                }
                            },
                            []
                        ]
                    }
                }
            },
            # Stage 4: Lookup task items
            {
                "$lookup": {
                    "from": "task_items",
                    "localField": "task_object_ids",
                    "foreignField": "_id",
                    "as": "task_items_details"
                }
            },

            # Stage 5: Unwind task items to process individually
            {
                "$unwind": {
                    "path": "$task_items_details",
                    "preserveNullAndEmptyArrays": True
                }
            },

            # Stage 6: Group by date, gentype and task item type
            {
                "$group": {
                    "_id": {
                        "date": "$date_only",
                        "gentype": "$gentype",
                        "task_type": "$task_items_details.type"
                    },
                    "count": {"$sum": 1}
                }
            },

            # Stage 7: Group by date and gentype to get task item types array
            {
                "$group": {
                    "_id": {
                        "date": "$_id.date",
                        "gentype": "$_id.gentype"
                    },
                    "total_task_items": {"$sum": "$count"},
                    "task_types": {
                        "$push": {
                            "task_type": "$_id.task_type",
                            "count": "$count"
                        }
                    }
                }
            },

            # Stage 8: Get task sets count for each date and gentype
            {
                "$lookup": {
                    "from": "task_sets",
                    "let": {"date": "$_id.date", "gentype": "$_id.gentype"},
                    "pipeline": [
                        {
                            "$addFields": {
                                "date_only": {
                                    "$dateToString": {
                                        "format": "%Y-%m-%d",
                                        "date": "$created_at"
                                    }
                                }
                            }
                        },
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$date_only", "$$date"]},
                                        {"$eq": ["$gentype", "$$gentype"]}
                                    ]
                                }
                            }
                        },
                        {"$count": "count"}
                    ],
                    "as": "task_sets_data"
                }
            },

            # Stage 9: Project clean structure for each date-gentype combination
            {
                "$project": {
                    "_id": 0,
                    "date": "$_id.date",
                    "gentype": "$_id.gentype",
                    "task_sets_count": {
                        "$ifNull": [
                            {"$arrayElemAt": ["$task_sets_data.count", 0]},
                            0
                        ]
                    },
                    "task_items_count": "$total_task_items",
                    "task_types": {
                        "$sortArray": {
                            "input": "$task_types",
                            "sortBy": {"count": -1}
                        }
                    }
                }
            },

            # Stage 10: Group by date to get all gentypes for each day
            {
                "$group": {
                    "_id": "$date",
                    "gentypes": {
                        "$push": {
                            "gentype": "$gentype",
                            "task_sets_count": "$task_sets_count",
                            "task_items_count": "$task_items_count",
                            "task_types": "$task_types"
                        }
                    },
                    "total_task_sets_for_date": {"$sum": "$task_sets_count"},
                    "total_task_items_for_date": {"$sum": "$task_items_count"}
                }
            },

            # Stage 11: Sort by date
            {
                "$sort": {"_id": 1}
            }
        ]



        # Execute hierarchical aggregation with error handling
        try:
            date_wise_results = await (await task_sets_collection.aggregate(task_sets_pipeline)).to_list(None)
        except Exception as agg_error:
            loggers.error(f"Aggregation pipeline error: {agg_error}")
            date_wise_results = []

        # Get total counts for all time (context)
        total_task_sets_all_time = await task_sets_collection.count_documents({})
        total_task_items_all_time = await task_items_collection.count_documents({})

        # Process date-wise data
        daily_data = []
        total_task_sets_in_range = 0
        total_task_items_in_range = 0
        colors = ['#F54F4A', '#FF8C75', '#FFB499', '#4CAF50', '#2196F3', '#FF9800']

        for date_data in date_wise_results or []:
            if not date_data:
                continue

            date = date_data.get("_id")
            gentypes = date_data.get("gentypes", [])
            date_total_sets = date_data.get("total_task_sets_for_date", 0)
            date_total_items = date_data.get("total_task_items_for_date", 0)

            # Update overall totals
            total_task_sets_in_range += date_total_sets
            total_task_items_in_range += date_total_items

            # Format gentype data for this date
            gentype_breakdown = []
            sunburst_data = []

            for i, gentype_data in enumerate(gentypes):
                if not gentype_data:
                    continue

                task_types_list = gentype_data.get("task_types", []) or []

                # Create children array for task item types
                children = []
                for task_type in task_types_list:
                    if task_type and isinstance(task_type, dict):
                        children.append({
                            "name": task_type.get("task_type", "unknown"),
                            "value": task_type.get("count", 0),
                            "itemStyle": {
                                "color": colors[(i + len(children)) % len(colors)]
                            }
                        })

                # Create gentype node for sunburst
                gentype_node = {
                    "name": gentype_data.get("gentype", "unknown"),
                    "value": gentype_data.get("task_sets_count", 0),
                    "task_items_count": gentype_data.get("task_items_count", 0),
                    "children": children,
                    "itemStyle": {
                        "color": colors[i % len(colors)]
                    }
                }
                sunburst_data.append(gentype_node)

                # Create gentype breakdown for easy access
                gentype_breakdown.append({
                    "gentype": gentype_data.get("gentype", "unknown"),
                    "task_sets_count": gentype_data.get("task_sets_count", 0),
                    "task_items_count": gentype_data.get("task_items_count", 0),
                    "task_types": task_types_list
                })

            # Create daily entry
            daily_entry = {
                "date": date,
                "total_task_sets": date_total_sets,
                "total_task_items": date_total_items,
                "gentype_breakdown": gentype_breakdown,
                "sunburst_data": sunburst_data
            }

            daily_data.append(daily_entry)

        # Create overall summary sunburst data (aggregated from all days)
        overall_sunburst_data = []
        gentype_totals = {}

        # Aggregate data across all days for overall view
        for daily_entry in daily_data:
            for gentype_data in daily_entry.get("gentype_breakdown", []):
                gentype = gentype_data.get("gentype", "unknown")
                if gentype not in gentype_totals:
                    gentype_totals[gentype] = {
                        "task_sets_count": 0,
                        "task_items_count": 0,
                        "task_types": {}
                    }

                gentype_totals[gentype]["task_sets_count"] += gentype_data.get("task_sets_count", 0)
                gentype_totals[gentype]["task_items_count"] += gentype_data.get("task_items_count", 0)

                # Aggregate task types
                for task_type in gentype_data.get("task_types", []):
                    if task_type and isinstance(task_type, dict):
                        type_name = task_type.get("task_type", "unknown")
                        if type_name not in gentype_totals[gentype]["task_types"]:
                            gentype_totals[gentype]["task_types"][type_name] = 0
                        gentype_totals[gentype]["task_types"][type_name] += task_type.get("count", 0)

        # Create overall sunburst data
        for i, (gentype, data) in enumerate(gentype_totals.items()):
            children = []
            for type_name, count in data["task_types"].items():
                children.append({
                    "name": type_name,
                    "value": count,
                    "itemStyle": {
                        "color": colors[(i + len(children)) % len(colors)]
                    }
                })

            overall_sunburst_data.append({
                "name": gentype,
                "value": data["task_sets_count"],
                "task_items_count": data["task_items_count"],
                "children": children,
                "itemStyle": {
                    "color": colors[i % len(colors)]
                }
            })

        hierarchy_data = {
            "name": "Total Task Sets",
            "value": total_task_sets_in_range,
            "children": overall_sunburst_data,
            "itemStyle": {
                "color": "#ddd"
            }
        }

        return {
            "timestamp": now.isoformat(),
            "date_range": {
                "start_date": start_dt.strftime("%Y-%m-%d"),
                "end_date": end_dt.strftime("%Y-%m-%d"),
                "total_days": len(daily_data)
            },
            "summary": {
                "total_task_sets_all_time": total_task_sets_all_time,
                "total_task_items_all_time": total_task_items_all_time,
                "total_task_sets_in_range": total_task_sets_in_range,
                "total_task_items_in_range": total_task_items_in_range
            },
            "daily_data": daily_data,
            "overall_sunburst_data": [hierarchy_data],
            "echarts_config": {
                "series": {
                    "radius": ["15%", "80%"],
                    "type": "sunburst",
                    "sort": None,
                    "emphasis": {
                        "focus": "ancestor"
                    },
                    "data": [hierarchy_data],
                    "label": {
                        "rotate": "radial"
                    },
                    "levels": [],
                    "itemStyle": {
                        "color": "#ddd",
                        "borderWidth": 2
                    }
                }
            }
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format. Use YYYY-MM-DD: {str(e)}")
    except Exception as e:
        loggers.error(f"Error getting task sets metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve task sets metrics: {str(e)}")
